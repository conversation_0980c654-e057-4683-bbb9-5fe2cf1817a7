{"name": "react-placeholder", "version": "4.1.0", "description": "A React component to easily replicate your page with nice placeholders while the content is loading", "main": "lib", "scripts": {"test": "jest", "build": "rm -rf lib && mkdir lib && node-sass src -o lib && tsc", "preversion": "npm run test", "prepublishOnly": "npm run build", "start": "styleguidist server", "typecheck": "tsc --noEmit", "release-version": "smooth-release"}, "repository": {"type": "git", "url": "**************:buildo/react-placeholder"}, "keywords": ["react", "react-component", "placeholder", "filler", "loading", "paragraph"], "author": "Francesco Cioria <<EMAIL>>", "license": "ISC", "bugs": {"url": "https://github.com/buildo/react-placeholder/issues"}, "files": ["lib"], "homepage": "https://github.com/buildo/react-placeholder", "typings": "lib", "dependencies": {}, "devDependencies": {"@testing-library/jest-dom": "^5.11.9", "@testing-library/react": "^11.2.5", "@types/enzyme": "^3.10.8", "@types/jest": "^26.0.20", "@types/lodash": "^4.14.168", "@types/node": "12.12.17", "@types/react": "^16.9.56", "babel-core": "^6.26.3", "babel-loader": "^7.1.5", "babel-preset-buildo": "^0.1.1", "css-loader": "^0.28.11", "enzyme": "^3.11.0", "enzyme-adapter-react-16": "^1.15.6", "file-loader": "^1.1.11", "jest": "^26.6.3", "lodash": "^4.17.20", "node-sass": "^4.14.1", "progress-bar-webpack-plugin": "^1.12.1", "raf": "^3.4.1", "raw-loader": "^0.5.1", "react": "^16.8.0", "react-docgen-typescript": "^1.16.1", "react-dom": "^16.8.0", "react-styleguidist": "^6.0.33", "react-test-renderer": "^16.8.0", "sass-loader": "^6.0.7", "smooth-release": "^8.0.9", "ts-jest": "^26.5.0", "ts-loader": "^3.5.0", "typescript": "~3.9.7", "webpack": "3.5.5"}, "peerDependencies": {"react": "^16.8.0 || ^17"}, "jest": {"testURL": "http://localhost", "setupFiles": ["raf/polyfill", "<rootDir>/tests/setup.js"], "transform": {"^.+\\.tsx?$": "ts-jest"}, "testRegex": "(.*[.](test))[.](tsx?)$", "moduleFileExtensions": ["js", "ts", "tsx"]}}