{"ast": null, "code": "// Firebase Realtime Database service\nimport { ref, onValue, set, update, remove, get } from 'firebase/database';\nimport { database } from './config';\nexport class FirebaseRealtimeService {\n  constructor() {\n    this.listeners = new Map();\n  }\n  /**\r\n   * Listen to room data changes\r\n   */\n  listenToRoom(roomId, callback) {\n    const roomRef = ref(database, `rooms/${roomId}`);\n    const unsubscribe = onValue(roomRef, snapshot => {\n      const data = snapshot.val();\n      callback(data);\n    });\n    const listenerId = `room_${roomId}`;\n    this.listeners.set(listenerId, unsubscribe);\n    return () => {\n      this.removeListener(listenerId);\n    };\n  }\n\n  /**\r\n   * Listen to player answers\r\n   */\n  listenToPlayerAnswers(roomId, callback) {\n    const answersRef = ref(database, `rooms/${roomId}/player_answer`);\n    const unsubscribe = onValue(answersRef, snapshot => {\n      const data = snapshot.val() || {};\n      callback(data);\n    });\n    const listenerId = `answers_${roomId}`;\n    this.listeners.set(listenerId, unsubscribe);\n    return () => {\n      this.removeListener(listenerId);\n    };\n  }\n\n  /**\r\n   * Listen to current question\r\n   */\n  listenToCurrentQuestion(roomId, callback) {\n    const questionRef = ref(database, `rooms/${roomId}/current_question`);\n    const unsubscribe = onValue(questionRef, snapshot => {\n      const data = snapshot.val();\n      callback(data);\n    });\n    const listenerId = `question_${roomId}`;\n    this.listeners.set(listenerId, unsubscribe);\n    return () => {\n      this.removeListener(listenerId);\n    };\n  }\n\n  /**\r\n   * Listen to scores\r\n   */\n  listenToScores(roomId, callback) {\n    const scoresRef = ref(database, `rooms/${roomId}/scores`);\n    const unsubscribe = onValue(scoresRef, snapshot => {\n      const data = snapshot.val() || [];\n      callback(Array.isArray(data) ? data : Object.values(data));\n    });\n    const listenerId = `scores_${roomId}`;\n    this.listeners.set(listenerId, unsubscribe);\n    return () => {\n      this.removeListener(listenerId);\n    };\n  }\n\n  /**\r\n   * Listen to game state\r\n   */\n  listenToGameState(roomId, callback) {\n    const gameStateRef = ref(database, `rooms/${roomId}/game_state`);\n    const unsubscribe = onValue(gameStateRef, snapshot => {\n      const data = snapshot.val();\n      callback(data);\n    });\n    const listenerId = `gameState_${roomId}`;\n    this.listeners.set(listenerId, unsubscribe);\n    return () => {\n      this.removeListener(listenerId);\n    };\n  }\n\n  /**\r\n   * Listen to Round 2 grid\r\n   */\n  listenToRound2Grid(roomId, callback) {\n    const gridRef = ref(database, `rooms/${roomId}/round2_grid`);\n    const unsubscribe = onValue(gridRef, snapshot => {\n      const data = snapshot.val();\n      callback(data);\n    });\n    const listenerId = `round2Grid_${roomId}`;\n    this.listeners.set(listenerId, unsubscribe);\n    return () => {\n      this.removeListener(listenerId);\n    };\n  }\n\n  /**\r\n   * Listen to Round 4 grid\r\n   */\n  listenToRound4Grid(roomId, callback) {\n    const gridRef = ref(database, `rooms/${roomId}/round4_grid`);\n    const unsubscribe = onValue(gridRef, snapshot => {\n      const data = snapshot.val();\n      callback(data);\n    });\n    const listenerId = `round4Grid_${roomId}`;\n    this.listeners.set(listenerId, unsubscribe);\n    return () => {\n      this.removeListener(listenerId);\n    };\n  }\n\n  /**\r\n   * Update player data\r\n   */\n  async updatePlayer(roomId, playerId, playerData) {\n    const playerRef = ref(database, `rooms/${roomId}/player_answer/${playerId}`);\n    await update(playerRef, playerData);\n  }\n\n  /**\r\n   * Set current question\r\n   */\n  async setCurrentQuestion(roomId, question) {\n    const questionRef = ref(database, `rooms/${roomId}/current_question`);\n    await set(questionRef, question);\n  }\n\n  /**\r\n   * Update scores\r\n   */\n  async updateScores(roomId, scores) {\n    const scoresRef = ref(database, `rooms/${roomId}/scores`);\n    await set(scoresRef, scores);\n  }\n\n  /**\r\n   * Update game state\r\n   */\n  async updateGameState(roomId, gameState) {\n    const gameStateRef = ref(database, `rooms/${roomId}/game_state`);\n    await update(gameStateRef, gameState);\n  }\n\n  /**\r\n   * Remove a specific listener\r\n   */\n  removeListener(listenerId) {\n    const unsubscribe = this.listeners.get(listenerId);\n    if (unsubscribe) {\n      unsubscribe();\n      this.listeners.delete(listenerId);\n    }\n  }\n\n  /**\r\n   * Remove all listeners\r\n   */\n  removeAllListeners() {\n    this.listeners.forEach(unsubscribe => {\n      unsubscribe();\n    });\n    this.listeners.clear();\n  }\n\n  /**\r\n   * Get data once (no listener)\r\n   */\n  async getData(path) {\n    const dataRef = ref(database, path);\n    const snapshot = await get(dataRef);\n    return snapshot.val();\n  }\n\n  /**\r\n   * Set data\r\n   */\n  async setData(path, data) {\n    const dataRef = ref(database, path);\n    await set(dataRef, data);\n  }\n\n  /**\r\n   * Update data\r\n   */\n  async updateData(path, updates) {\n    const dataRef = ref(database, path);\n    await update(dataRef, updates);\n  }\n\n  /**\r\n   * Remove data\r\n   */\n  async removeData(path) {\n    const dataRef = ref(database, path);\n    await remove(dataRef);\n  }\n}\n\n// Create singleton instance\nexport const firebaseRealtimeService = new FirebaseRealtimeService();\nexport default firebaseRealtimeService;", "map": {"version": 3, "names": ["ref", "onValue", "set", "update", "remove", "get", "database", "FirebaseRealtimeService", "constructor", "listeners", "Map", "listenToRoom", "roomId", "callback", "roomRef", "unsubscribe", "snapshot", "data", "val", "listenerId", "removeListener", "listenToPlayerAnswers", "answersRef", "listenToCurrentQuestion", "questionRef", "listenToScores", "scoresRef", "Array", "isArray", "Object", "values", "listenToGameState", "gameStateRef", "listenToRound2Grid", "gridRef", "listenToRound4Grid", "updatePlayer", "playerId", "player<PERSON><PERSON>", "playerRef", "setCurrentQuestion", "question", "updateScores", "scores", "updateGameState", "gameState", "delete", "removeAllListeners", "for<PERSON>ach", "clear", "getData", "path", "dataRef", "setData", "updateData", "updates", "removeData", "firebaseRealtimeService"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/shared/services/firebase/realtime.ts"], "sourcesContent": ["// Firebase Realtime Database service\r\nimport { \r\n  ref, \r\n  onValue, \r\n  off, \r\n  set, \r\n  update, \r\n  remove, \r\n  push, \r\n  get,\r\n  DatabaseReference,\r\n  DataSnapshot,\r\n  Unsubscribe\r\n} from 'firebase/database';\r\nimport { database } from './config';\r\nimport { PlayerData, Question, Score } from '../../types';\r\n\r\nexport class FirebaseRealtimeService {\r\n  private listeners: Map<string, Unsubscribe> = new Map();\r\n\r\n  /**\r\n   * Listen to room data changes\r\n   */\r\n  listenToRoom(roomId: string, callback: (data: any) => void): () => void {\r\n    const roomRef = ref(database, `rooms/${roomId}`);\r\n    \r\n    const unsubscribe = onValue(roomRef, (snapshot: DataSnapshot) => {\r\n      const data = snapshot.val();\r\n      callback(data);\r\n    });\r\n\r\n    const listenerId = `room_${roomId}`;\r\n    this.listeners.set(listenerId, unsubscribe);\r\n\r\n    return () => {\r\n      this.removeListener(listenerId);\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Listen to player answers\r\n   */\r\n  listenToPlayerAnswers(roomId: string, callback: (answers: Record<string, PlayerData>) => void): () => void {\r\n    const answersRef = ref(database, `rooms/${roomId}/player_answer`);\r\n    \r\n    const unsubscribe = onValue(answersRef, (snapshot: DataSnapshot) => {\r\n      const data = snapshot.val() || {};\r\n      callback(data);\r\n    });\r\n\r\n    const listenerId = `answers_${roomId}`;\r\n    this.listeners.set(listenerId, unsubscribe);\r\n\r\n    return () => {\r\n      this.removeListener(listenerId);\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Listen to current question\r\n   */\r\n  listenToCurrentQuestion(roomId: string, callback: (question: Question | null) => void): () => void {\r\n    const questionRef = ref(database, `rooms/${roomId}/current_question`);\r\n    \r\n    const unsubscribe = onValue(questionRef, (snapshot: DataSnapshot) => {\r\n      const data = snapshot.val();\r\n      callback(data);\r\n    });\r\n\r\n    const listenerId = `question_${roomId}`;\r\n    this.listeners.set(listenerId, unsubscribe);\r\n\r\n    return () => {\r\n      this.removeListener(listenerId);\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Listen to scores\r\n   */\r\n  listenToScores(roomId: string, callback: (scores: Score[]) => void): () => void {\r\n    const scoresRef = ref(database, `rooms/${roomId}/scores`);\r\n    \r\n    const unsubscribe = onValue(scoresRef, (snapshot: DataSnapshot) => {\r\n      const data = snapshot.val() || [];\r\n      callback(Array.isArray(data) ? data : Object.values(data));\r\n    });\r\n\r\n    const listenerId = `scores_${roomId}`;\r\n    this.listeners.set(listenerId, unsubscribe);\r\n\r\n    return () => {\r\n      this.removeListener(listenerId);\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Listen to game state\r\n   */\r\n  listenToGameState(roomId: string, callback: (state: any) => void): () => void {\r\n    const gameStateRef = ref(database, `rooms/${roomId}/game_state`);\r\n    \r\n    const unsubscribe = onValue(gameStateRef, (snapshot: DataSnapshot) => {\r\n      const data = snapshot.val();\r\n      callback(data);\r\n    });\r\n\r\n    const listenerId = `gameState_${roomId}`;\r\n    this.listeners.set(listenerId, unsubscribe);\r\n\r\n    return () => {\r\n      this.removeListener(listenerId);\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Listen to Round 2 grid\r\n   */\r\n  listenToRound2Grid(roomId: string, callback: (grid: any) => void): () => void {\r\n    const gridRef = ref(database, `rooms/${roomId}/round2_grid`);\r\n    \r\n    const unsubscribe = onValue(gridRef, (snapshot: DataSnapshot) => {\r\n      const data = snapshot.val();\r\n      callback(data);\r\n    });\r\n\r\n    const listenerId = `round2Grid_${roomId}`;\r\n    this.listeners.set(listenerId, unsubscribe);\r\n\r\n    return () => {\r\n      this.removeListener(listenerId);\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Listen to Round 4 grid\r\n   */\r\n  listenToRound4Grid(roomId: string, callback: (grid: any) => void): () => void {\r\n    const gridRef = ref(database, `rooms/${roomId}/round4_grid`);\r\n    \r\n    const unsubscribe = onValue(gridRef, (snapshot: DataSnapshot) => {\r\n      const data = snapshot.val();\r\n      callback(data);\r\n    });\r\n\r\n    const listenerId = `round4Grid_${roomId}`;\r\n    this.listeners.set(listenerId, unsubscribe);\r\n\r\n    return () => {\r\n      this.removeListener(listenerId);\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Update player data\r\n   */\r\n  async updatePlayer(roomId: string, playerId: string, playerData: Partial<PlayerData>): Promise<void> {\r\n    const playerRef = ref(database, `rooms/${roomId}/player_answer/${playerId}`);\r\n    await update(playerRef, playerData);\r\n  }\r\n\r\n  /**\r\n   * Set current question\r\n   */\r\n  async setCurrentQuestion(roomId: string, question: Question): Promise<void> {\r\n    const questionRef = ref(database, `rooms/${roomId}/current_question`);\r\n    await set(questionRef, question);\r\n  }\r\n\r\n  /**\r\n   * Update scores\r\n   */\r\n  async updateScores(roomId: string, scores: Score[]): Promise<void> {\r\n    const scoresRef = ref(database, `rooms/${roomId}/scores`);\r\n    await set(scoresRef, scores);\r\n  }\r\n\r\n  /**\r\n   * Update game state\r\n   */\r\n  async updateGameState(roomId: string, gameState: any): Promise<void> {\r\n    const gameStateRef = ref(database, `rooms/${roomId}/game_state`);\r\n    await update(gameStateRef, gameState);\r\n  }\r\n\r\n  /**\r\n   * Remove a specific listener\r\n   */\r\n  removeListener(listenerId: string): void {\r\n    const unsubscribe = this.listeners.get(listenerId);\r\n    if (unsubscribe) {\r\n      unsubscribe();\r\n      this.listeners.delete(listenerId);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Remove all listeners\r\n   */\r\n  removeAllListeners(): void {\r\n    this.listeners.forEach((unsubscribe) => {\r\n      unsubscribe();\r\n    });\r\n    this.listeners.clear();\r\n  }\r\n\r\n  /**\r\n   * Get data once (no listener)\r\n   */\r\n  async getData(path: string): Promise<any> {\r\n    const dataRef = ref(database, path);\r\n    const snapshot = await get(dataRef);\r\n    return snapshot.val();\r\n  }\r\n\r\n  /**\r\n   * Set data\r\n   */\r\n  async setData(path: string, data: any): Promise<void> {\r\n    const dataRef = ref(database, path);\r\n    await set(dataRef, data);\r\n  }\r\n\r\n  /**\r\n   * Update data\r\n   */\r\n  async updateData(path: string, updates: any): Promise<void> {\r\n    const dataRef = ref(database, path);\r\n    await update(dataRef, updates);\r\n  }\r\n\r\n  /**\r\n   * Remove data\r\n   */\r\n  async removeData(path: string): Promise<void> {\r\n    const dataRef = ref(database, path);\r\n    await remove(dataRef);\r\n  }\r\n}\r\n\r\n// Create singleton instance\r\nexport const firebaseRealtimeService = new FirebaseRealtimeService();\r\n\r\nexport default firebaseRealtimeService;\r\n"], "mappings": "AAAA;AACA,SACEA,GAAG,EACHC,OAAO,EAEPC,GAAG,EACHC,MAAM,EACNC,MAAM,EAENC,GAAG,QAIE,mBAAmB;AAC1B,SAASC,QAAQ,QAAQ,UAAU;AAGnC,OAAO,MAAMC,uBAAuB,CAAC;EAAAC,YAAA;IAAA,KAC3BC,SAAS,GAA6B,IAAIC,GAAG,CAAC,CAAC;EAAA;EAEvD;AACF;AACA;EACEC,YAAYA,CAACC,MAAc,EAAEC,QAA6B,EAAc;IACtE,MAAMC,OAAO,GAAGd,GAAG,CAACM,QAAQ,EAAE,SAASM,MAAM,EAAE,CAAC;IAEhD,MAAMG,WAAW,GAAGd,OAAO,CAACa,OAAO,EAAGE,QAAsB,IAAK;MAC/D,MAAMC,IAAI,GAAGD,QAAQ,CAACE,GAAG,CAAC,CAAC;MAC3BL,QAAQ,CAACI,IAAI,CAAC;IAChB,CAAC,CAAC;IAEF,MAAME,UAAU,GAAG,QAAQP,MAAM,EAAE;IACnC,IAAI,CAACH,SAAS,CAACP,GAAG,CAACiB,UAAU,EAAEJ,WAAW,CAAC;IAE3C,OAAO,MAAM;MACX,IAAI,CAACK,cAAc,CAACD,UAAU,CAAC;IACjC,CAAC;EACH;;EAEA;AACF;AACA;EACEE,qBAAqBA,CAACT,MAAc,EAAEC,QAAuD,EAAc;IACzG,MAAMS,UAAU,GAAGtB,GAAG,CAACM,QAAQ,EAAE,SAASM,MAAM,gBAAgB,CAAC;IAEjE,MAAMG,WAAW,GAAGd,OAAO,CAACqB,UAAU,EAAGN,QAAsB,IAAK;MAClE,MAAMC,IAAI,GAAGD,QAAQ,CAACE,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;MACjCL,QAAQ,CAACI,IAAI,CAAC;IAChB,CAAC,CAAC;IAEF,MAAME,UAAU,GAAG,WAAWP,MAAM,EAAE;IACtC,IAAI,CAACH,SAAS,CAACP,GAAG,CAACiB,UAAU,EAAEJ,WAAW,CAAC;IAE3C,OAAO,MAAM;MACX,IAAI,CAACK,cAAc,CAACD,UAAU,CAAC;IACjC,CAAC;EACH;;EAEA;AACF;AACA;EACEI,uBAAuBA,CAACX,MAAc,EAAEC,QAA6C,EAAc;IACjG,MAAMW,WAAW,GAAGxB,GAAG,CAACM,QAAQ,EAAE,SAASM,MAAM,mBAAmB,CAAC;IAErE,MAAMG,WAAW,GAAGd,OAAO,CAACuB,WAAW,EAAGR,QAAsB,IAAK;MACnE,MAAMC,IAAI,GAAGD,QAAQ,CAACE,GAAG,CAAC,CAAC;MAC3BL,QAAQ,CAACI,IAAI,CAAC;IAChB,CAAC,CAAC;IAEF,MAAME,UAAU,GAAG,YAAYP,MAAM,EAAE;IACvC,IAAI,CAACH,SAAS,CAACP,GAAG,CAACiB,UAAU,EAAEJ,WAAW,CAAC;IAE3C,OAAO,MAAM;MACX,IAAI,CAACK,cAAc,CAACD,UAAU,CAAC;IACjC,CAAC;EACH;;EAEA;AACF;AACA;EACEM,cAAcA,CAACb,MAAc,EAAEC,QAAmC,EAAc;IAC9E,MAAMa,SAAS,GAAG1B,GAAG,CAACM,QAAQ,EAAE,SAASM,MAAM,SAAS,CAAC;IAEzD,MAAMG,WAAW,GAAGd,OAAO,CAACyB,SAAS,EAAGV,QAAsB,IAAK;MACjE,MAAMC,IAAI,GAAGD,QAAQ,CAACE,GAAG,CAAC,CAAC,IAAI,EAAE;MACjCL,QAAQ,CAACc,KAAK,CAACC,OAAO,CAACX,IAAI,CAAC,GAAGA,IAAI,GAAGY,MAAM,CAACC,MAAM,CAACb,IAAI,CAAC,CAAC;IAC5D,CAAC,CAAC;IAEF,MAAME,UAAU,GAAG,UAAUP,MAAM,EAAE;IACrC,IAAI,CAACH,SAAS,CAACP,GAAG,CAACiB,UAAU,EAAEJ,WAAW,CAAC;IAE3C,OAAO,MAAM;MACX,IAAI,CAACK,cAAc,CAACD,UAAU,CAAC;IACjC,CAAC;EACH;;EAEA;AACF;AACA;EACEY,iBAAiBA,CAACnB,MAAc,EAAEC,QAA8B,EAAc;IAC5E,MAAMmB,YAAY,GAAGhC,GAAG,CAACM,QAAQ,EAAE,SAASM,MAAM,aAAa,CAAC;IAEhE,MAAMG,WAAW,GAAGd,OAAO,CAAC+B,YAAY,EAAGhB,QAAsB,IAAK;MACpE,MAAMC,IAAI,GAAGD,QAAQ,CAACE,GAAG,CAAC,CAAC;MAC3BL,QAAQ,CAACI,IAAI,CAAC;IAChB,CAAC,CAAC;IAEF,MAAME,UAAU,GAAG,aAAaP,MAAM,EAAE;IACxC,IAAI,CAACH,SAAS,CAACP,GAAG,CAACiB,UAAU,EAAEJ,WAAW,CAAC;IAE3C,OAAO,MAAM;MACX,IAAI,CAACK,cAAc,CAACD,UAAU,CAAC;IACjC,CAAC;EACH;;EAEA;AACF;AACA;EACEc,kBAAkBA,CAACrB,MAAc,EAAEC,QAA6B,EAAc;IAC5E,MAAMqB,OAAO,GAAGlC,GAAG,CAACM,QAAQ,EAAE,SAASM,MAAM,cAAc,CAAC;IAE5D,MAAMG,WAAW,GAAGd,OAAO,CAACiC,OAAO,EAAGlB,QAAsB,IAAK;MAC/D,MAAMC,IAAI,GAAGD,QAAQ,CAACE,GAAG,CAAC,CAAC;MAC3BL,QAAQ,CAACI,IAAI,CAAC;IAChB,CAAC,CAAC;IAEF,MAAME,UAAU,GAAG,cAAcP,MAAM,EAAE;IACzC,IAAI,CAACH,SAAS,CAACP,GAAG,CAACiB,UAAU,EAAEJ,WAAW,CAAC;IAE3C,OAAO,MAAM;MACX,IAAI,CAACK,cAAc,CAACD,UAAU,CAAC;IACjC,CAAC;EACH;;EAEA;AACF;AACA;EACEgB,kBAAkBA,CAACvB,MAAc,EAAEC,QAA6B,EAAc;IAC5E,MAAMqB,OAAO,GAAGlC,GAAG,CAACM,QAAQ,EAAE,SAASM,MAAM,cAAc,CAAC;IAE5D,MAAMG,WAAW,GAAGd,OAAO,CAACiC,OAAO,EAAGlB,QAAsB,IAAK;MAC/D,MAAMC,IAAI,GAAGD,QAAQ,CAACE,GAAG,CAAC,CAAC;MAC3BL,QAAQ,CAACI,IAAI,CAAC;IAChB,CAAC,CAAC;IAEF,MAAME,UAAU,GAAG,cAAcP,MAAM,EAAE;IACzC,IAAI,CAACH,SAAS,CAACP,GAAG,CAACiB,UAAU,EAAEJ,WAAW,CAAC;IAE3C,OAAO,MAAM;MACX,IAAI,CAACK,cAAc,CAACD,UAAU,CAAC;IACjC,CAAC;EACH;;EAEA;AACF;AACA;EACE,MAAMiB,YAAYA,CAACxB,MAAc,EAAEyB,QAAgB,EAAEC,UAA+B,EAAiB;IACnG,MAAMC,SAAS,GAAGvC,GAAG,CAACM,QAAQ,EAAE,SAASM,MAAM,kBAAkByB,QAAQ,EAAE,CAAC;IAC5E,MAAMlC,MAAM,CAACoC,SAAS,EAAED,UAAU,CAAC;EACrC;;EAEA;AACF;AACA;EACE,MAAME,kBAAkBA,CAAC5B,MAAc,EAAE6B,QAAkB,EAAiB;IAC1E,MAAMjB,WAAW,GAAGxB,GAAG,CAACM,QAAQ,EAAE,SAASM,MAAM,mBAAmB,CAAC;IACrE,MAAMV,GAAG,CAACsB,WAAW,EAAEiB,QAAQ,CAAC;EAClC;;EAEA;AACF;AACA;EACE,MAAMC,YAAYA,CAAC9B,MAAc,EAAE+B,MAAe,EAAiB;IACjE,MAAMjB,SAAS,GAAG1B,GAAG,CAACM,QAAQ,EAAE,SAASM,MAAM,SAAS,CAAC;IACzD,MAAMV,GAAG,CAACwB,SAAS,EAAEiB,MAAM,CAAC;EAC9B;;EAEA;AACF;AACA;EACE,MAAMC,eAAeA,CAAChC,MAAc,EAAEiC,SAAc,EAAiB;IACnE,MAAMb,YAAY,GAAGhC,GAAG,CAACM,QAAQ,EAAE,SAASM,MAAM,aAAa,CAAC;IAChE,MAAMT,MAAM,CAAC6B,YAAY,EAAEa,SAAS,CAAC;EACvC;;EAEA;AACF;AACA;EACEzB,cAAcA,CAACD,UAAkB,EAAQ;IACvC,MAAMJ,WAAW,GAAG,IAAI,CAACN,SAAS,CAACJ,GAAG,CAACc,UAAU,CAAC;IAClD,IAAIJ,WAAW,EAAE;MACfA,WAAW,CAAC,CAAC;MACb,IAAI,CAACN,SAAS,CAACqC,MAAM,CAAC3B,UAAU,CAAC;IACnC;EACF;;EAEA;AACF;AACA;EACE4B,kBAAkBA,CAAA,EAAS;IACzB,IAAI,CAACtC,SAAS,CAACuC,OAAO,CAAEjC,WAAW,IAAK;MACtCA,WAAW,CAAC,CAAC;IACf,CAAC,CAAC;IACF,IAAI,CAACN,SAAS,CAACwC,KAAK,CAAC,CAAC;EACxB;;EAEA;AACF;AACA;EACE,MAAMC,OAAOA,CAACC,IAAY,EAAgB;IACxC,MAAMC,OAAO,GAAGpD,GAAG,CAACM,QAAQ,EAAE6C,IAAI,CAAC;IACnC,MAAMnC,QAAQ,GAAG,MAAMX,GAAG,CAAC+C,OAAO,CAAC;IACnC,OAAOpC,QAAQ,CAACE,GAAG,CAAC,CAAC;EACvB;;EAEA;AACF;AACA;EACE,MAAMmC,OAAOA,CAACF,IAAY,EAAElC,IAAS,EAAiB;IACpD,MAAMmC,OAAO,GAAGpD,GAAG,CAACM,QAAQ,EAAE6C,IAAI,CAAC;IACnC,MAAMjD,GAAG,CAACkD,OAAO,EAAEnC,IAAI,CAAC;EAC1B;;EAEA;AACF;AACA;EACE,MAAMqC,UAAUA,CAACH,IAAY,EAAEI,OAAY,EAAiB;IAC1D,MAAMH,OAAO,GAAGpD,GAAG,CAACM,QAAQ,EAAE6C,IAAI,CAAC;IACnC,MAAMhD,MAAM,CAACiD,OAAO,EAAEG,OAAO,CAAC;EAChC;;EAEA;AACF;AACA;EACE,MAAMC,UAAUA,CAACL,IAAY,EAAiB;IAC5C,MAAMC,OAAO,GAAGpD,GAAG,CAACM,QAAQ,EAAE6C,IAAI,CAAC;IACnC,MAAM/C,MAAM,CAACgD,OAAO,CAAC;EACvB;AACF;;AAEA;AACA,OAAO,MAAMK,uBAAuB,GAAG,IAAIlD,uBAAuB,CAAC,CAAC;AAEpE,eAAekD,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}