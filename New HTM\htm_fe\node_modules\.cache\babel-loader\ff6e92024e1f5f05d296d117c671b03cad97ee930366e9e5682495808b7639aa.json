{"ast": null, "code": "export const processFile = file => {\n  if (!file) {\n    throw new Error(\"Không có file nào được chọn!\");\n  }\n  console.log(\"file\", file);\n  const formData = new FormData();\n  formData.append(\"file\", file);\n  console.log(\"form data\", formData);\n  return formData;\n};", "map": {"version": 3, "names": ["processFile", "file", "Error", "console", "log", "formData", "FormData", "append"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/utils/processFile.utils.ts"], "sourcesContent": ["export const processFile = (file:File) => {\r\n    if (!file) {\r\n        throw new Error(\"Không có file nào được chọn!\");\r\n    }\r\n\r\n    console.log(\"file\", file);\r\n    \r\n\r\n    const formData = new FormData();\r\n    formData.append(\"file\", file);\r\n\r\n    console.log(\"form data\", formData);\r\n    \r\n\r\n    return formData\r\n}"], "mappings": "AAAA,OAAO,MAAMA,WAAW,GAAIC,IAAS,IAAK;EACtC,IAAI,CAACA,IAAI,EAAE;IACP,MAAM,IAAIC,KAAK,CAAC,8BAA8B,CAAC;EACnD;EAEAC,OAAO,CAACC,GAAG,CAAC,MAAM,EAAEH,IAAI,CAAC;EAGzB,MAAMI,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;EAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEN,IAAI,CAAC;EAE7BE,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEC,QAAQ,CAAC;EAGlC,OAAOA,QAAQ;AACnB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}