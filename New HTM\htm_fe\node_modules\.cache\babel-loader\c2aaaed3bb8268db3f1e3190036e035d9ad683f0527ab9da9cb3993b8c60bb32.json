{"ast": null, "code": "// API client configuration with interceptors\nimport axios from 'axios';\nimport { API_BASE_URL, REQUEST_TIMEOUT } from '../../constants';\n\n// Extend InternalAxiosRequestConfig to include _retry property\n\n// Create axios instance\nconst apiClient = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: REQUEST_TIMEOUT,\n  headers: {\n    'Content-Type': 'application/json'\n  },\n  withCredentials: true\n});\n\n// Request interceptor\napiClient.interceptors.request.use(config => {\n  // Add auth token if available\n  const accessToken = localStorage.getItem('accessToken');\n  if (accessToken && config.headers) {\n    config.headers.Authorization = `Bearer ${accessToken}`;\n  }\n\n  // Log request in development\n  if (process.env.NODE_ENV === 'development') {\n    var _config$method;\n    console.log(`🚀 API Request: ${(_config$method = config.method) === null || _config$method === void 0 ? void 0 : _config$method.toUpperCase()} ${config.url}`, {\n      data: config.data,\n      params: config.params\n    });\n  }\n  return config;\n}, error => {\n  console.error('❌ Request Error:', error);\n  return Promise.reject(error);\n});\n\n// Response interceptor\napiClient.interceptors.response.use(response => {\n  // Log response in development\n  if (process.env.NODE_ENV === 'development') {\n    var _response$config$meth;\n    console.log(`✅ API Response: ${(_response$config$meth = response.config.method) === null || _response$config$meth === void 0 ? void 0 : _response$config$meth.toUpperCase()} ${response.config.url}`, {\n      status: response.status,\n      data: response.data\n    });\n  }\n  return response;\n}, async error => {\n  var _error$response, _error$response2, _error$response2$data, _error$response3, _error$response3$data, _error$response4, _error$response4$data;\n  const originalRequest = error.config;\n\n  // Handle 401 errors (token expired)\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401 && originalRequest && !originalRequest._retry) {\n    originalRequest._retry = true;\n    try {\n      // Try to refresh token\n      const refreshToken = localStorage.getItem('refreshToken');\n      if (refreshToken) {\n        const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {\n          refreshToken\n        });\n        const {\n          accessToken\n        } = response.data.data;\n        localStorage.setItem('accessToken', accessToken);\n\n        // Retry original request with new token\n        if (originalRequest.headers) {\n          originalRequest.headers.Authorization = `Bearer ${accessToken}`;\n        }\n        return apiClient(originalRequest);\n      }\n    } catch (refreshError) {\n      // Refresh failed, redirect to login\n      localStorage.removeItem('accessToken');\n      localStorage.removeItem('refreshToken');\n      window.location.href = '/login';\n      return Promise.reject(refreshError);\n    }\n  }\n\n  // Handle other errors\n  const apiError = {\n    message: ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || error.message || 'An error occurred',\n    code: ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.code) || 'UNKNOWN_ERROR',\n    details: (_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.details,\n    timestamp: new Date().toISOString()\n  };\n\n  // Log error in development\n  if (process.env.NODE_ENV === 'development') {\n    var _error$config, _error$config$method, _error$config2, _error$response5;\n    console.error(`❌ API Error: ${(_error$config = error.config) === null || _error$config === void 0 ? void 0 : (_error$config$method = _error$config.method) === null || _error$config$method === void 0 ? void 0 : _error$config$method.toUpperCase()} ${(_error$config2 = error.config) === null || _error$config2 === void 0 ? void 0 : _error$config2.url}`, {\n      status: (_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : _error$response5.status,\n      error: apiError\n    });\n  }\n  return Promise.reject(apiError);\n});\n\n// Generic API methods\nexport const api = {\n  get: (url, config) => apiClient.get(url, config),\n  post: (url, data, config) => apiClient.post(url, data, config),\n  put: (url, data, config) => apiClient.put(url, data, config),\n  patch: (url, data, config) => apiClient.patch(url, data, config),\n  delete: (url, config) => apiClient.delete(url, config)\n};\nexport default apiClient;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "REQUEST_TIMEOUT", "apiClient", "create", "baseURL", "timeout", "headers", "withCredentials", "interceptors", "request", "use", "config", "accessToken", "localStorage", "getItem", "Authorization", "process", "env", "NODE_ENV", "_config$method", "console", "log", "method", "toUpperCase", "url", "data", "params", "error", "Promise", "reject", "response", "_response$config$meth", "status", "_error$response", "_error$response2", "_error$response2$data", "_error$response3", "_error$response3$data", "_error$response4", "_error$response4$data", "originalRequest", "_retry", "refreshToken", "post", "setItem", "refreshError", "removeItem", "window", "location", "href", "apiError", "message", "code", "details", "timestamp", "Date", "toISOString", "_error$config", "_error$config$method", "_error$config2", "_error$response5", "api", "get", "put", "patch", "delete"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/shared/services/api/client.ts"], "sourcesContent": ["// API client configuration with interceptors\r\nimport axios, { AxiosInstance, AxiosResponse, AxiosError, InternalAxiosRequestConfig } from 'axios';\r\nimport { API_BASE_URL, REQUEST_TIMEOUT } from '../../constants';\r\nimport { ApiResponse, ApiError } from '../../types';\r\n\r\n// Extend InternalAxiosRequestConfig to include _retry property\r\ninterface ExtendedAxiosRequestConfig extends InternalAxiosRequestConfig {\r\n  _retry?: boolean;\r\n}\r\n\r\n// Create axios instance\r\nconst apiClient: AxiosInstance = axios.create({\r\n  baseURL: API_BASE_URL,\r\n  timeout: REQUEST_TIMEOUT,\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n  },\r\n  withCredentials: true,\r\n});\r\n\r\n// Request interceptor\r\napiClient.interceptors.request.use(\r\n  (config: InternalAxiosRequestConfig) => {\r\n    // Add auth token if available\r\n    const accessToken = localStorage.getItem('accessToken');\r\n    if (accessToken && config.headers) {\r\n      config.headers.Authorization = `Bearer ${accessToken}`;\r\n    }\r\n\r\n    // Log request in development\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`, {\r\n        data: config.data,\r\n        params: config.params,\r\n      });\r\n    }\r\n\r\n    return config;\r\n  },\r\n  (error: AxiosError) => {\r\n    console.error('❌ Request Error:', error);\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Response interceptor\r\napiClient.interceptors.response.use(\r\n  (response: AxiosResponse<ApiResponse>) => {\r\n    // Log response in development\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url}`, {\r\n        status: response.status,\r\n        data: response.data,\r\n      });\r\n    }\r\n\r\n    return response;\r\n  },\r\n  async (error: AxiosError<ApiError>) => {\r\n    const originalRequest = error.config as ExtendedAxiosRequestConfig;\r\n\r\n    // Handle 401 errors (token expired)\r\n    if (error.response?.status === 401 && originalRequest && !originalRequest._retry) {\r\n      originalRequest._retry = true;\r\n\r\n      try {\r\n        // Try to refresh token\r\n        const refreshToken = localStorage.getItem('refreshToken');\r\n        if (refreshToken) {\r\n          const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {\r\n            refreshToken,\r\n          });\r\n\r\n          const { accessToken } = response.data.data;\r\n          localStorage.setItem('accessToken', accessToken);\r\n\r\n          // Retry original request with new token\r\n          if (originalRequest.headers) {\r\n            originalRequest.headers.Authorization = `Bearer ${accessToken}`;\r\n          }\r\n          return apiClient(originalRequest);\r\n        }\r\n      } catch (refreshError) {\r\n        // Refresh failed, redirect to login\r\n        localStorage.removeItem('accessToken');\r\n        localStorage.removeItem('refreshToken');\r\n        window.location.href = '/login';\r\n        return Promise.reject(refreshError);\r\n      }\r\n    }\r\n\r\n    // Handle other errors\r\n    const apiError: ApiError = {\r\n      message: error.response?.data?.message || error.message || 'An error occurred',\r\n      code: error.response?.data?.code || 'UNKNOWN_ERROR',\r\n      details: error.response?.data?.details,\r\n      timestamp: new Date().toISOString(),\r\n    };\r\n\r\n    // Log error in development\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.error(`❌ API Error: ${error.config?.method?.toUpperCase()} ${error.config?.url}`, {\r\n        status: error.response?.status,\r\n        error: apiError,\r\n      });\r\n    }\r\n\r\n    return Promise.reject(apiError);\r\n  }\r\n);\r\n\r\n// Generic API methods\r\nexport const api = {\r\n  get: <T = any>(url: string, config?: Partial<InternalAxiosRequestConfig>): Promise<AxiosResponse<ApiResponse<T>>> =>\r\n    apiClient.get(url, config),\r\n\r\n  post: <T = any>(url: string, data?: any, config?: Partial<InternalAxiosRequestConfig>): Promise<AxiosResponse<ApiResponse<T>>> =>\r\n    apiClient.post(url, data, config),\r\n\r\n  put: <T = any>(url: string, data?: any, config?: Partial<InternalAxiosRequestConfig>): Promise<AxiosResponse<ApiResponse<T>>> =>\r\n    apiClient.put(url, data, config),\r\n\r\n  patch: <T = any>(url: string, data?: any, config?: Partial<InternalAxiosRequestConfig>): Promise<AxiosResponse<ApiResponse<T>>> =>\r\n    apiClient.patch(url, data, config),\r\n\r\n  delete: <T = any>(url: string, config?: Partial<InternalAxiosRequestConfig>): Promise<AxiosResponse<ApiResponse<T>>> =>\r\n    apiClient.delete(url, config),\r\n};\r\n\r\nexport default apiClient;\r\n"], "mappings": "AAAA;AACA,OAAOA,KAAK,MAAgF,OAAO;AACnG,SAASC,YAAY,EAAEC,eAAe,QAAQ,iBAAiB;;AAG/D;;AAKA;AACA,MAAMC,SAAwB,GAAGH,KAAK,CAACI,MAAM,CAAC;EAC5CC,OAAO,EAAEJ,YAAY;EACrBK,OAAO,EAAEJ,eAAe;EACxBK,OAAO,EAAE;IACP,cAAc,EAAE;EAClB,CAAC;EACDC,eAAe,EAAE;AACnB,CAAC,CAAC;;AAEF;AACAL,SAAS,CAACM,YAAY,CAACC,OAAO,CAACC,GAAG,CAC/BC,MAAkC,IAAK;EACtC;EACA,MAAMC,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;EACvD,IAAIF,WAAW,IAAID,MAAM,CAACL,OAAO,EAAE;IACjCK,MAAM,CAACL,OAAO,CAACS,aAAa,GAAG,UAAUH,WAAW,EAAE;EACxD;;EAEA;EACA,IAAII,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;IAAA,IAAAC,cAAA;IAC1CC,OAAO,CAACC,GAAG,CAAC,oBAAAF,cAAA,GAAmBR,MAAM,CAACW,MAAM,cAAAH,cAAA,uBAAbA,cAAA,CAAeI,WAAW,CAAC,CAAC,IAAIZ,MAAM,CAACa,GAAG,EAAE,EAAE;MAC3EC,IAAI,EAAEd,MAAM,CAACc,IAAI;MACjBC,MAAM,EAAEf,MAAM,CAACe;IACjB,CAAC,CAAC;EACJ;EAEA,OAAOf,MAAM;AACf,CAAC,EACAgB,KAAiB,IAAK;EACrBP,OAAO,CAACO,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;EACxC,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAzB,SAAS,CAACM,YAAY,CAACsB,QAAQ,CAACpB,GAAG,CAChCoB,QAAoC,IAAK;EACxC;EACA,IAAId,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;IAAA,IAAAa,qBAAA;IAC1CX,OAAO,CAACC,GAAG,CAAC,oBAAAU,qBAAA,GAAmBD,QAAQ,CAACnB,MAAM,CAACW,MAAM,cAAAS,qBAAA,uBAAtBA,qBAAA,CAAwBR,WAAW,CAAC,CAAC,IAAIO,QAAQ,CAACnB,MAAM,CAACa,GAAG,EAAE,EAAE;MAC7FQ,MAAM,EAAEF,QAAQ,CAACE,MAAM;MACvBP,IAAI,EAAEK,QAAQ,CAACL;IACjB,CAAC,CAAC;EACJ;EAEA,OAAOK,QAAQ;AACjB,CAAC,EACD,MAAOH,KAA2B,IAAK;EAAA,IAAAM,eAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA;EACrC,MAAMC,eAAe,GAAGb,KAAK,CAAChB,MAAoC;;EAElE;EACA,IAAI,EAAAsB,eAAA,GAAAN,KAAK,CAACG,QAAQ,cAAAG,eAAA,uBAAdA,eAAA,CAAgBD,MAAM,MAAK,GAAG,IAAIQ,eAAe,IAAI,CAACA,eAAe,CAACC,MAAM,EAAE;IAChFD,eAAe,CAACC,MAAM,GAAG,IAAI;IAE7B,IAAI;MACF;MACA,MAAMC,YAAY,GAAG7B,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;MACzD,IAAI4B,YAAY,EAAE;QAChB,MAAMZ,QAAQ,GAAG,MAAM/B,KAAK,CAAC4C,IAAI,CAAC,GAAG3C,YAAY,eAAe,EAAE;UAChE0C;QACF,CAAC,CAAC;QAEF,MAAM;UAAE9B;QAAY,CAAC,GAAGkB,QAAQ,CAACL,IAAI,CAACA,IAAI;QAC1CZ,YAAY,CAAC+B,OAAO,CAAC,aAAa,EAAEhC,WAAW,CAAC;;QAEhD;QACA,IAAI4B,eAAe,CAAClC,OAAO,EAAE;UAC3BkC,eAAe,CAAClC,OAAO,CAACS,aAAa,GAAG,UAAUH,WAAW,EAAE;QACjE;QACA,OAAOV,SAAS,CAACsC,eAAe,CAAC;MACnC;IACF,CAAC,CAAC,OAAOK,YAAY,EAAE;MACrB;MACAhC,YAAY,CAACiC,UAAU,CAAC,aAAa,CAAC;MACtCjC,YAAY,CAACiC,UAAU,CAAC,cAAc,CAAC;MACvCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;MAC/B,OAAOrB,OAAO,CAACC,MAAM,CAACgB,YAAY,CAAC;IACrC;EACF;;EAEA;EACA,MAAMK,QAAkB,GAAG;IACzBC,OAAO,EAAE,EAAAjB,gBAAA,GAAAP,KAAK,CAACG,QAAQ,cAAAI,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBT,IAAI,cAAAU,qBAAA,uBAApBA,qBAAA,CAAsBgB,OAAO,KAAIxB,KAAK,CAACwB,OAAO,IAAI,mBAAmB;IAC9EC,IAAI,EAAE,EAAAhB,gBAAA,GAAAT,KAAK,CAACG,QAAQ,cAAAM,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBX,IAAI,cAAAY,qBAAA,uBAApBA,qBAAA,CAAsBe,IAAI,KAAI,eAAe;IACnDC,OAAO,GAAAf,gBAAA,GAAEX,KAAK,CAACG,QAAQ,cAAAQ,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBb,IAAI,cAAAc,qBAAA,uBAApBA,qBAAA,CAAsBc,OAAO;IACtCC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;EACpC,CAAC;;EAED;EACA,IAAIxC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;IAAA,IAAAuC,aAAA,EAAAC,oBAAA,EAAAC,cAAA,EAAAC,gBAAA;IAC1CxC,OAAO,CAACO,KAAK,CAAC,iBAAA8B,aAAA,GAAgB9B,KAAK,CAAChB,MAAM,cAAA8C,aAAA,wBAAAC,oBAAA,GAAZD,aAAA,CAAcnC,MAAM,cAAAoC,oBAAA,uBAApBA,oBAAA,CAAsBnC,WAAW,CAAC,CAAC,KAAAoC,cAAA,GAAIhC,KAAK,CAAChB,MAAM,cAAAgD,cAAA,uBAAZA,cAAA,CAAcnC,GAAG,EAAE,EAAE;MACxFQ,MAAM,GAAA4B,gBAAA,GAAEjC,KAAK,CAACG,QAAQ,cAAA8B,gBAAA,uBAAdA,gBAAA,CAAgB5B,MAAM;MAC9BL,KAAK,EAAEuB;IACT,CAAC,CAAC;EACJ;EAEA,OAAOtB,OAAO,CAACC,MAAM,CAACqB,QAAQ,CAAC;AACjC,CACF,CAAC;;AAED;AACA,OAAO,MAAMW,GAAG,GAAG;EACjBC,GAAG,EAAEA,CAAUtC,GAAW,EAAEb,MAA4C,KACtET,SAAS,CAAC4D,GAAG,CAACtC,GAAG,EAAEb,MAAM,CAAC;EAE5BgC,IAAI,EAAEA,CAAUnB,GAAW,EAAEC,IAAU,EAAEd,MAA4C,KACnFT,SAAS,CAACyC,IAAI,CAACnB,GAAG,EAAEC,IAAI,EAAEd,MAAM,CAAC;EAEnCoD,GAAG,EAAEA,CAAUvC,GAAW,EAAEC,IAAU,EAAEd,MAA4C,KAClFT,SAAS,CAAC6D,GAAG,CAACvC,GAAG,EAAEC,IAAI,EAAEd,MAAM,CAAC;EAElCqD,KAAK,EAAEA,CAAUxC,GAAW,EAAEC,IAAU,EAAEd,MAA4C,KACpFT,SAAS,CAAC8D,KAAK,CAACxC,GAAG,EAAEC,IAAI,EAAEd,MAAM,CAAC;EAEpCsD,MAAM,EAAEA,CAAUzC,GAAW,EAAEb,MAA4C,KACzET,SAAS,CAAC+D,MAAM,CAACzC,GAAG,EAAEb,MAAM;AAChC,CAAC;AAED,eAAeT,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}