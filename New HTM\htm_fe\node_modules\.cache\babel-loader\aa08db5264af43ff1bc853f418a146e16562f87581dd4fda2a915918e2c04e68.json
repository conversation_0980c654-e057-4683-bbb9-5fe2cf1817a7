{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\New HTM\\\\htm_fe\\\\src\\\\components\\\\HostGuideModal.tsx\";\nimport React from 'react';\nimport { XMarkIcon, QuestionMarkCircleIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst GUIDE_CONTENT = {\n  \"1\": {\n    title: \"HƯỚNG DẪN HOST - VÒNG 1: NHỔ NEO\",\n    steps: [\"1. Bấm 'Bắt đầu vòng thi' để bắt đầu vòng thi\", \"2. Bấm 'Chạy âm thanh bắt đầu vòng thi'\", \"3. Bấ<PERSON> 'Câu hỏi tiếp theo' để hiển thị câu hỏi\", \"4. Bấm 'Đếm giờ' để bắt đầu thời gian trả lời (15s)\", \"5. <PERSON><PERSON> <PERSON><PERSON> hết giờ, bấm 'Hiện đáp án' để hiển thị đáp án của thí sinh\", \"5. <PERSON><PERSON> <PERSON>hi hết giờ, bấm 'Hiện đáp án đúng' để hiển thị đáp án đúng của câu hỏi\", \"6. Bấm 'Chấm điểm' (tự động theo thời gian hoặc thủ công tùy theo mode đã chọn)\", \"7. Lặp lại bước 3-6 cho 10 câu hỏi\"]\n  },\n  \"2\": {\n    title: \"HƯỚNG DẪN HOST - VÒNG 2: VƯỢT SÓNG\",\n    steps: [\"1. Bấm 'Bắt đầu vòng thi' để bắt đầu vòng thi\", \"2. Bấm 'Chạy âm thanh bắt đầu vòng thi'\", \"3. Bấm vào số thứ tự hàng ngang để mở menu hành động\", \"4. Bấm 'SELECT' để hiển thị câu hỏi cho hàng ngang\", \"5. Bấm 'Đếm giờ' để bắt đầu thời gian trả lời\", \"6. Sau khi hết giờ, bấm 'Hiện đáp án thí sinh' để hiển thị câu trả lời của thí sinh\", \"7. Bấm vào số thứ tự hàng ngang để mở menu hành động, chọn correct nếu có thí sinh trả lời đúng và incorrect nếu không ai trả lời đúng\", \"8. Bấm 'Chấm điểm tự động'\", \"9. <b>Đặc biệt:</b> Nếu thí sinh trả lời đúng chướng ngại vật:\", \"   - Bấm 'Mở chướng ngại vật' trước\", \"   - Chọn thí sinh đã trả lời đúng\", \"   - Bấm 'Cập nhật lượt thi'\", \"   - Bấm 'Chấm điểm CNV' với điểm thưởng tương ứng\", \"10. Lặp lại cho các câu hỏi còn lại\"]\n  },\n  \"3\": {\n    title: \"HƯỚNG DẪN HOST - VÒNG 3: BỨT PHÁ\",\n    steps: [\"1. <b>Giai đoạn phân lượt:</b>\", \"   - Xác định thứ tự thi thông qua phần thi phân lượt\", \"   - Ghi nhận thứ tự thí sinh qua thanh điều khiển dưới mỗi thí sinh\", \"2. <b>Giai đoạn thi chính:</b>\", \"   - Bấm 'Bắt đầu vòng thi'\", \"   - Chọn thí sinh theo thứ tự đã xác định\", \"   - Thí sinh chọn gói\", \"   - Chọn gói tương ứng\", \"   - Bấm 'Câu hỏi tiếp theo' để chạy câu đầu tiên (60s)\", \"3. <b>Trong quá trình thi:</b>\", \"   - Với các câu tiếp theo: chỉ cần bấm 'Đúng'/'Sai'\", \"   - Hệ thống sẽ tự động chuyển câu khi bấm đúng/sai\", \"   - Mỗi câu đúng = 10 điểm (hoặc theo cấu hình)\", \"4. Lặp lại cho tất cả thí sinh (2 lượt/người)\"]\n  },\n  \"4\": {\n    title: \"HƯỚNG DẪN HOST - VÒNG 4: CHINH PHỤC\",\n    steps: [\"1. <b>Chuẩn bị:</b>\", \"   - Chọn màu cho từng thí sinh trước khi bắt đầu\", \"   - Màu sẽ hiển thị trong ô thí sinh\", \"2. <b>Giai đoạn phân lượt:</b>\", \"   - Sau khi kết thúc vòng thi phân lượt, yêu cầu thí sinh chọn thứ tự thi cho mình dựa theo thành tích ở vòng phân lượt\", \"3. <b>Giai đoạn thi chính:</b>\", \"   - Chọn thí sinh theo lượt\", \"   - Thí sinh chọn 1 ô trên bảng 5x5\", \"   - Bấm 'Select' trên ô đã chọn\", \"   - Bấm 'Câu hỏi tiếp theo'\", \"4. <b>Xử lý kết quả:</b>\", \"   - Bấm vào thí sinh đang thi\", \"   - Bấm 'Cập nhật lượt chơi'\", \"   - Bấm 'Đúng'/'Sai'/'Sai NSHV' tương ứng\", \"5. <b>Khi có thí sinh sai hoặc sai NSHV:</b>\", \"   - Hệ thống tự động mở chuông\", \"   - Nếu có thí sinh giành lượt: chọn ô của thí sinh đó (KHÔNG bấm cập nhật lượt)\", \"   - Bấm 'Giành lượt đúng'/'Giành lượt sai'\", \"6. <b>Khi thí sinh trả lời đúng:</b>\", \"   - Chọn ô đã trả lời đúng\", \"   - Tô màu tương ứng với thí sinh\", \"7. Lặp lại cho đến khi hoàn thành trò chơi\"]\n  },\n  \"turn\": {\n    title: \"HƯỚNG DẪN HOST - VÒNG PHÂN LƯỢT\",\n    steps: [\"1. Bấm 'Bắt đầu vòng phân lượt'\", \"2. Bấm 'Câu hỏi tiếp theo' để hiển thị câu hỏi\", \"3. Bấm 'Đếm giờ' để bắt đầu thời gian\", \"4. Sau khi hết giờ, bấm 'Hiện đáp án' để hiển thị đáp án và thời gian trả lời của thí sinh\", \"5. Xác định thứ tự thí sinh dựa trên tốc độ trả lời\", \"6. Ghi nhận thứ tự qua thanh điều khiển\", \"7. Chuyển sang vòng thi chính\"]\n  }\n};\nconst HostGuideModal = ({\n  isOpen,\n  onClose,\n  round\n}) => {\n  if (!isOpen) return null;\n  const guide = GUIDE_CONTENT[round];\n  if (!guide) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 z-50 flex items-center justify-center\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 bg-black/70 backdrop-blur-sm\",\n      onClick: onClose\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl shadow-2xl border border-green-400/30 max-w-3xl w-full mx-4 max-h-[85vh] overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-green-600 to-emerald-600 px-6 py-4 flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(QuestionMarkCircleIcon, {\n            className: \"w-8 h-8 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold text-white\",\n            children: guide.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onClose,\n          className: \"text-white hover:text-gray-300 transition-colors p-1 rounded-lg hover:bg-white/10\",\n          children: /*#__PURE__*/_jsxDEV(XMarkIcon, {\n            className: \"w-6 h-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6 overflow-y-auto max-h-[70vh]\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: guide.steps.map((step, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start space-x-3 p-4 bg-slate-700/50 rounded-lg border border-slate-600/50 hover:bg-slate-700/70 transition-colors\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-shrink-0 w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold\",\n              children: index + 1\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-gray-200 text-base leading-relaxed flex-1\",\n              dangerouslySetInnerHTML: {\n                __html: step\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6 p-4 bg-blue-900/30 rounded-lg border border-blue-400/30\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-blue-200 font-semibold mb-2 flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"w-2 h-2 bg-blue-400 rounded-full mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this), \"L\\u01B0u \\xFD quan tr\\u1ECDng:\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"text-blue-100 text-sm space-y-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u2022 Lu\\xF4n ki\\u1EC3m tra mode ch\\u1EA5m \\u0111i\\u1EC3m (th\\u1EE7 c\\xF4ng/t\\u1EF1 \\u0111\\u1ED9ng) tr\\u01B0\\u1EDBc khi b\\u1EAFt \\u0111\\u1EA7u\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u2022 Quan s\\xE1t ph\\u1EA3n \\u1EE9ng c\\u1EE7a th\\xED sinh \\u0111\\u1EC3 \\u0111i\\u1EC1u ch\\u1EC9nh t\\u1ED1c \\u0111\\u1ED9 ph\\xF9 h\\u1EE3p\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u2022 S\\u1EED d\\u1EE5ng n\\xFAt \\\"Hi\\u1EC3n th\\u1ECB lu\\u1EADt thi\\\" khi c\\u1EA7n thi\\u1EBFt\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u2022 Ki\\u1EC3m tra k\\u1EBFt n\\u1ED1i m\\u1EA1ng c\\u1EE7a t\\u1EA5t c\\u1EA3 th\\xED sinh tr\\u01B0\\u1EDBc khi b\\u1EAFt \\u0111\\u1EA7u\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-slate-800/80 px-6 py-4 border-t border-slate-600/50\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onClose,\n            className: \"px-6 py-2 bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 text-white rounded-lg font-medium transition-all duration-200 hover:scale-105\",\n            children: \"\\u0110\\xE3 hi\\u1EC3u\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 113,\n    columnNumber: 5\n  }, this);\n};\n_c = HostGuideModal;\nexport default HostGuideModal;\nvar _c;\n$RefreshReg$(_c, \"HostGuideModal\");", "map": {"version": 3, "names": ["React", "XMarkIcon", "QuestionMarkCircleIcon", "jsxDEV", "_jsxDEV", "GUIDE_CONTENT", "title", "steps", "HostGuideModal", "isOpen", "onClose", "round", "guide", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "step", "index", "dangerouslySetInnerHTML", "__html", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/components/HostGuideModal.tsx"], "sourcesContent": ["import React from 'react';\nimport { XMarkIcon, QuestionMarkCircleIcon } from '@heroicons/react/24/outline';\n\ninterface HostGuideModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  round: string;\n}\n\nconst GUIDE_CONTENT = {\n  \"1\": {\n    title: \"HƯỚNG DẪN HOST - VÒNG 1: NHỔ NEO\",\n    steps: [\n      \"1. Bấm 'Bắt đầu vòng thi' để bắt đầu vòng thi\",\n      \"2. Bấm 'Chạy âm thanh bắt đầu vòng thi'\",\n      \"3. Bấm 'Câu hỏi tiếp theo' để hiển thị câu hỏi\",\n      \"4. Bấm 'Đếm giờ' để bắt đầu thời gian trả lời (15s)\",\n      \"5. <PERSON><PERSON> khi hết giờ, bấm 'Hiện đáp án' để hiển thị đáp án của thí sinh\",\n      \"5. <PERSON><PERSON> <PERSON>hi hết giờ, bấm 'Hiện đáp án đúng' để hiển thị đáp án đúng của câu hỏi\",\n      \"6. <PERSON><PERSON><PERSON> 'Chấm điểm' (tự động theo thời gian hoặc thủ công tùy theo mode đã chọn)\",\n      \"7. Lặp lại bước 3-6 cho 10 câu hỏi\"\n    ]\n  },\n  \"2\": {\n    title: \"HƯỚNG DẪN HOST - VÒNG 2: VƯỢT SÓNG\",\n    steps: [\n      \"1. Bấm 'Bắt đầu vòng thi' để bắt đầu vòng thi\",\n      \"2. Bấm 'Chạy âm thanh bắt đầu vòng thi'\",\n      \"3. Bấm vào số thứ tự hàng ngang để mở menu hành động\",\n      \"4. Bấm 'SELECT' để hiển thị câu hỏi cho hàng ngang\",\n      \"5. Bấm 'Đếm giờ' để bắt đầu thời gian trả lời\",\n      \"6. Sau khi hết giờ, bấm 'Hiện đáp án thí sinh' để hiển thị câu trả lời của thí sinh\",\n      \"7. Bấm vào số thứ tự hàng ngang để mở menu hành động, chọn correct nếu có thí sinh trả lời đúng và incorrect nếu không ai trả lời đúng\",\n      \"8. Bấm 'Chấm điểm tự động'\",\n      \"9. <b>Đặc biệt:</b> Nếu thí sinh trả lời đúng chướng ngại vật:\",\n      \"   - Bấm 'Mở chướng ngại vật' trước\",\n      \"   - Chọn thí sinh đã trả lời đúng\",\n      \"   - Bấm 'Cập nhật lượt thi'\",\n      \"   - Bấm 'Chấm điểm CNV' với điểm thưởng tương ứng\",\n      \"10. Lặp lại cho các câu hỏi còn lại\"\n    ]\n  },\n  \"3\": {\n    title: \"HƯỚNG DẪN HOST - VÒNG 3: BỨT PHÁ\",\n    steps: [\n      \"1. <b>Giai đoạn phân lượt:</b>\",\n      \"   - Xác định thứ tự thi thông qua phần thi phân lượt\",\n      \"   - Ghi nhận thứ tự thí sinh qua thanh điều khiển dưới mỗi thí sinh\",\n      \"2. <b>Giai đoạn thi chính:</b>\",\n      \"   - Bấm 'Bắt đầu vòng thi'\",\n      \"   - Chọn thí sinh theo thứ tự đã xác định\",\n      \"   - Thí sinh chọn gói\",\n      \"   - Chọn gói tương ứng\",\n      \"   - Bấm 'Câu hỏi tiếp theo' để chạy câu đầu tiên (60s)\",\n      \"3. <b>Trong quá trình thi:</b>\",\n      \"   - Với các câu tiếp theo: chỉ cần bấm 'Đúng'/'Sai'\",\n      \"   - Hệ thống sẽ tự động chuyển câu khi bấm đúng/sai\",\n      \"   - Mỗi câu đúng = 10 điểm (hoặc theo cấu hình)\",\n      \"4. Lặp lại cho tất cả thí sinh (2 lượt/người)\"\n    ]\n  },\n  \"4\": {\n    title: \"HƯỚNG DẪN HOST - VÒNG 4: CHINH PHỤC\",\n    steps: [\n      \"1. <b>Chuẩn bị:</b>\",\n      \"   - Chọn màu cho từng thí sinh trước khi bắt đầu\",\n      \"   - Màu sẽ hiển thị trong ô thí sinh\",\n      \"2. <b>Giai đoạn phân lượt:</b>\",\n      \"   - Sau khi kết thúc vòng thi phân lượt, yêu cầu thí sinh chọn thứ tự thi cho mình dựa theo thành tích ở vòng phân lượt\",\n      \"3. <b>Giai đoạn thi chính:</b>\",\n      \"   - Chọn thí sinh theo lượt\",\n      \"   - Thí sinh chọn 1 ô trên bảng 5x5\",\n      \"   - Bấm 'Select' trên ô đã chọn\",\n      \"   - Bấm 'Câu hỏi tiếp theo'\",\n      \"4. <b>Xử lý kết quả:</b>\",\n      \"   - Bấm vào thí sinh đang thi\",\n      \"   - Bấm 'Cập nhật lượt chơi'\",\n      \"   - Bấm 'Đúng'/'Sai'/'Sai NSHV' tương ứng\",\n      \"5. <b>Khi có thí sinh sai hoặc sai NSHV:</b>\",\n      \"   - Hệ thống tự động mở chuông\",\n      \"   - Nếu có thí sinh giành lượt: chọn ô của thí sinh đó (KHÔNG bấm cập nhật lượt)\",\n      \"   - Bấm 'Giành lượt đúng'/'Giành lượt sai'\",\n      \"6. <b>Khi thí sinh trả lời đúng:</b>\",\n      \"   - Chọn ô đã trả lời đúng\",\n      \"   - Tô màu tương ứng với thí sinh\",\n      \"7. Lặp lại cho đến khi hoàn thành trò chơi\"\n    ]\n  },\n  \"turn\": {\n    title: \"HƯỚNG DẪN HOST - VÒNG PHÂN LƯỢT\",\n    steps: [\n      \"1. Bấm 'Bắt đầu vòng phân lượt'\",\n      \"2. Bấm 'Câu hỏi tiếp theo' để hiển thị câu hỏi\",\n      \"3. Bấm 'Đếm giờ' để bắt đầu thời gian\",\n      \"4. Sau khi hết giờ, bấm 'Hiện đáp án' để hiển thị đáp án và thời gian trả lời của thí sinh\",\n      \"5. Xác định thứ tự thí sinh dựa trên tốc độ trả lời\",\n      \"6. Ghi nhận thứ tự qua thanh điều khiển\",\n      \"7. Chuyển sang vòng thi chính\"\n    ]\n  }\n};\n\nconst HostGuideModal: React.FC<HostGuideModalProps> = ({ isOpen, onClose, round }) => {\n  if (!isOpen) return null;\n\n  const guide = GUIDE_CONTENT[round as keyof typeof GUIDE_CONTENT];\n\n  if (!guide) {\n    return null;\n  }\n\n  return (\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center\">\n      {/* Backdrop */}\n      <div\n        className=\"absolute inset-0 bg-black/70 backdrop-blur-sm\"\n        onClick={onClose}\n      />\n\n      {/* Modal */}\n      <div className=\"relative bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl shadow-2xl border border-green-400/30 max-w-3xl w-full mx-4 max-h-[85vh] overflow-hidden\">\n        {/* Header */}\n        <div className=\"bg-gradient-to-r from-green-600 to-emerald-600 px-6 py-4 flex items-center justify-between\">\n          <div className=\"flex items-center space-x-3\">\n            <QuestionMarkCircleIcon className=\"w-8 h-8 text-white\" />\n            <h2 className=\"text-xl font-bold text-white\">\n              {guide.title}\n            </h2>\n          </div>\n          <button\n            onClick={onClose}\n            className=\"text-white hover:text-gray-300 transition-colors p-1 rounded-lg hover:bg-white/10\"\n          >\n            <XMarkIcon className=\"w-6 h-6\" />\n          </button>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-6 overflow-y-auto max-h-[70vh]\">\n          <div className=\"space-y-4\">\n            {guide.steps.map((step: string, index: number) => (\n              <div\n                key={index}\n                className=\"flex items-start space-x-3 p-4 bg-slate-700/50 rounded-lg border border-slate-600/50 hover:bg-slate-700/70 transition-colors\"\n              >\n                <div className=\"flex-shrink-0 w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold\">\n                  {index + 1}\n                </div>\n                <div \n                  className=\"text-gray-200 text-base leading-relaxed flex-1\"\n                  dangerouslySetInnerHTML={{ __html: step }}\n                />\n              </div>\n            ))}\n          </div>\n          \n          {/* Additional Tips */}\n          <div className=\"mt-6 p-4 bg-blue-900/30 rounded-lg border border-blue-400/30\">\n            <h3 className=\"text-blue-200 font-semibold mb-2 flex items-center\">\n              <span className=\"w-2 h-2 bg-blue-400 rounded-full mr-2\"></span>\n              Lưu ý quan trọng:\n            </h3>\n            <ul className=\"text-blue-100 text-sm space-y-1\">\n              <li>• Luôn kiểm tra mode chấm điểm (thủ công/tự động) trước khi bắt đầu</li>\n              <li>• Quan sát phản ứng của thí sinh để điều chỉnh tốc độ phù hợp</li>\n              <li>• Sử dụng nút \"Hiển thị luật thi\" khi cần thiết</li>\n              <li>• Kiểm tra kết nối mạng của tất cả thí sinh trước khi bắt đầu</li>\n            </ul>\n          </div>\n        </div>\n\n        {/* Footer */}\n        <div className=\"bg-slate-800/80 px-6 py-4 border-t border-slate-600/50\">\n          <div className=\"flex justify-center\">\n            <button\n              onClick={onClose}\n              className=\"px-6 py-2 bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 text-white rounded-lg font-medium transition-all duration-200 hover:scale-105\"\n            >\n              Đã hiểu\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default HostGuideModal;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,EAAEC,sBAAsB,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQhF,MAAMC,aAAa,GAAG;EACpB,GAAG,EAAE;IACHC,KAAK,EAAE,kCAAkC;IACzCC,KAAK,EAAE,CACL,+CAA+C,EAC/C,yCAAyC,EACzC,gDAAgD,EAChD,qDAAqD,EACrD,uEAAuE,EACvE,gFAAgF,EAChF,iFAAiF,EACjF,oCAAoC;EAExC,CAAC;EACD,GAAG,EAAE;IACHD,KAAK,EAAE,oCAAoC;IAC3CC,KAAK,EAAE,CACL,+CAA+C,EAC/C,yCAAyC,EACzC,sDAAsD,EACtD,oDAAoD,EACpD,+CAA+C,EAC/C,qFAAqF,EACrF,wIAAwI,EACxI,4BAA4B,EAC5B,gEAAgE,EAChE,qCAAqC,EACrC,oCAAoC,EACpC,8BAA8B,EAC9B,oDAAoD,EACpD,qCAAqC;EAEzC,CAAC;EACD,GAAG,EAAE;IACHD,KAAK,EAAE,kCAAkC;IACzCC,KAAK,EAAE,CACL,gCAAgC,EAChC,uDAAuD,EACvD,sEAAsE,EACtE,gCAAgC,EAChC,6BAA6B,EAC7B,4CAA4C,EAC5C,wBAAwB,EACxB,yBAAyB,EACzB,yDAAyD,EACzD,gCAAgC,EAChC,sDAAsD,EACtD,sDAAsD,EACtD,kDAAkD,EAClD,+CAA+C;EAEnD,CAAC;EACD,GAAG,EAAE;IACHD,KAAK,EAAE,qCAAqC;IAC5CC,KAAK,EAAE,CACL,qBAAqB,EACrB,mDAAmD,EACnD,uCAAuC,EACvC,gCAAgC,EAChC,0HAA0H,EAC1H,gCAAgC,EAChC,8BAA8B,EAC9B,sCAAsC,EACtC,kCAAkC,EAClC,8BAA8B,EAC9B,0BAA0B,EAC1B,gCAAgC,EAChC,+BAA+B,EAC/B,4CAA4C,EAC5C,8CAA8C,EAC9C,iCAAiC,EACjC,mFAAmF,EACnF,6CAA6C,EAC7C,sCAAsC,EACtC,6BAA6B,EAC7B,oCAAoC,EACpC,4CAA4C;EAEhD,CAAC;EACD,MAAM,EAAE;IACND,KAAK,EAAE,iCAAiC;IACxCC,KAAK,EAAE,CACL,iCAAiC,EACjC,gDAAgD,EAChD,uCAAuC,EACvC,4FAA4F,EAC5F,qDAAqD,EACrD,yCAAyC,EACzC,+BAA+B;EAEnC;AACF,CAAC;AAED,MAAMC,cAA6C,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC;AAAM,CAAC,KAAK;EACpF,IAAI,CAACF,MAAM,EAAE,OAAO,IAAI;EAExB,MAAMG,KAAK,GAAGP,aAAa,CAACM,KAAK,CAA+B;EAEhE,IAAI,CAACC,KAAK,EAAE;IACV,OAAO,IAAI;EACb;EAEA,oBACER,OAAA;IAAKS,SAAS,EAAC,qDAAqD;IAAAC,QAAA,gBAElEV,OAAA;MACES,SAAS,EAAC,+CAA+C;MACzDE,OAAO,EAAEL;IAAQ;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC,eAGFf,OAAA;MAAKS,SAAS,EAAC,6JAA6J;MAAAC,QAAA,gBAE1KV,OAAA;QAAKS,SAAS,EAAC,4FAA4F;QAAAC,QAAA,gBACzGV,OAAA;UAAKS,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CV,OAAA,CAACF,sBAAsB;YAACW,SAAS,EAAC;UAAoB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzDf,OAAA;YAAIS,SAAS,EAAC,8BAA8B;YAAAC,QAAA,EACzCF,KAAK,CAACN;UAAK;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACNf,OAAA;UACEW,OAAO,EAAEL,OAAQ;UACjBG,SAAS,EAAC,mFAAmF;UAAAC,QAAA,eAE7FV,OAAA,CAACH,SAAS;YAACY,SAAS,EAAC;UAAS;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNf,OAAA;QAAKS,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/CV,OAAA;UAAKS,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBF,KAAK,CAACL,KAAK,CAACa,GAAG,CAAC,CAACC,IAAY,EAAEC,KAAa,kBAC3ClB,OAAA;YAEES,SAAS,EAAC,8HAA8H;YAAAC,QAAA,gBAExIV,OAAA;cAAKS,SAAS,EAAC,+GAA+G;cAAAC,QAAA,EAC3HQ,KAAK,GAAG;YAAC;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eACNf,OAAA;cACES,SAAS,EAAC,gDAAgD;cAC1DU,uBAAuB,EAAE;gBAAEC,MAAM,EAAEH;cAAK;YAAE;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA,GATGG,KAAK;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUP,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNf,OAAA;UAAKS,SAAS,EAAC,8DAA8D;UAAAC,QAAA,gBAC3EV,OAAA;YAAIS,SAAS,EAAC,oDAAoD;YAAAC,QAAA,gBAChEV,OAAA;cAAMS,SAAS,EAAC;YAAuC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,kCAEjE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLf,OAAA;YAAIS,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC7CV,OAAA;cAAAU,QAAA,EAAI;YAAmE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5Ef,OAAA;cAAAU,QAAA,EAAI;YAA6D;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtEf,OAAA;cAAAU,QAAA,EAAI;YAA+C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxDf,OAAA;cAAAU,QAAA,EAAI;YAA6D;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNf,OAAA;QAAKS,SAAS,EAAC,wDAAwD;QAAAC,QAAA,eACrEV,OAAA;UAAKS,SAAS,EAAC,qBAAqB;UAAAC,QAAA,eAClCV,OAAA;YACEW,OAAO,EAAEL,OAAQ;YACjBG,SAAS,EAAC,8KAA8K;YAAAC,QAAA,EACzL;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACM,EAAA,GAnFIjB,cAA6C;AAqFnD,eAAeA,cAAc;AAAC,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}