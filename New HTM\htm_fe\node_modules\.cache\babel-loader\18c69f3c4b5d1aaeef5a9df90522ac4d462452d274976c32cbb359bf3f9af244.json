{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\New HTM\\\\htm_fe\\\\src\\\\layouts\\\\RoundBase\\\\Player\\\\PlayerQuestionBoxRoundTurn.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { listenToTimeStart, listenToQuestions, listenToAnswers, listenToSound, deletePath } from '../../../services/firebaseServices';\nimport { useSearchParams } from 'react-router-dom';\nimport { useHost } from '../../../context/hostContext';\nimport { useTimeStart } from '../../../context/timeListenerContext';\nimport { usePlayer } from '../../../context/playerContext';\nimport PlayerAnswerInput from '../../../components/ui/PlayerAnswerInput';\nimport { submitAnswer } from '../../services';\nimport { useSounds } from '../../../context/soundContext';\n\n// interface QuestionBoxProps {\n//     question: string;\n//     imgUrl?: string;\n//     isHost?: boolean\n// }\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PlayerQuestionBoxRoundTurn = ({\n  isHost,\n  isSpectator = false\n}) => {\n  _s();\n  const sounds = useSounds();\n  const [searchParams] = useSearchParams();\n  const roomId = searchParams.get(\"roomId\") || \"\";\n  const [currentQuestion, setCurrentQuestion] = useState();\n  const [correctAnswer, setCorrectAnswer] = useState(\"\");\n  const [isExpanded, setIsExpanded] = useState(false);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const {\n    timeLeft,\n    playerAnswerTime,\n    startTimer,\n    setTimeLeft\n  } = useTimeStart();\n  const {\n    setAnswerList,\n    playerAnswerRef,\n    position,\n    animationKey,\n    setAnimationKey,\n    currentPlayerName,\n    currentPlayerAvatar\n  } = usePlayer();\n  const {\n    currentAnswer\n  } = useHost();\n  useEffect(() => {\n    console.log(\"playerAnswerRef.current\", playerAnswerRef.current);\n  }, [playerAnswerRef.current]);\n  // useEffect(() => {\n  //     if (isInitialMount) return\n\n  //     startTimer(10);\n\n  //     return () => {\n  //         // Clean up timer\n  //     }\n\n  // }, []);\n  const isInitialMount = useRef(false);\n  useEffect(() => {\n    const unsubscribe = listenToTimeStart(roomId, async () => {\n      // Skip the timer setting on the first mount, but allow future calls to run\n      if (isInitialMount.current) {\n        isInitialMount.current = false;\n        return;\n      }\n      startTimer(10);\n      return () => {\n        unsubscribe();\n      };\n    });\n  }, []);\n  const isInitialTimerMount = useRef(true);\n  useEffect(() => {\n    console.log(\"timeLeft\", timeLeft);\n    if (isInitialTimerMount.current) {\n      isInitialTimerMount.current = false;\n      return;\n    }\n    if (timeLeft === 0) {\n      setAnimationKey(prev => prev + 1);\n      if (!isHost && !isSpectator) {\n        console.log(\"playerAnswerRef.current\", playerAnswerRef.current);\n        console.log(\"position\", position);\n\n        // When timer runs out, do your clean up / game logic:\n        submitAnswer(roomId, playerAnswerRef.current, position, playerAnswerTime, currentPlayerName, currentPlayerAvatar);\n      }\n      // If you want to reset timer, call startTimer again here or leave stopped\n    }\n  }, [timeLeft]);\n  useEffect(() => {\n    const unsubscribePlayers = listenToQuestions(roomId, question => {\n      setCurrentQuestion(question);\n      console.log(\"isHost\", isHost);\n      console.log(\"current correcr Answer\", currentAnswer);\n      if (isHost) {\n        setCorrectAnswer(currentAnswer);\n      }\n      console.log(\"current question\", question);\n      setAnswerList(null);\n      if (!isHost) {\n        setCorrectAnswer(\"\");\n      }\n    });\n\n    // No need to set state here; it's handled by useState initializer\n    return () => {\n      unsubscribePlayers();\n    };\n  }, []);\n  useEffect(() => {\n    const unsubscribePlayers = listenToSound(roomId, async type => {\n      const audio = sounds[`${type}`];\n      if (audio) {\n        audio.play();\n      }\n      console.log(\"sound type\", type);\n      await deletePath(roomId, \"sound\");\n    });\n\n    // No need to set state here; it's handled by useState initializer\n    return () => {\n      unsubscribePlayers();\n    };\n  }, []);\n  useEffect(() => {\n    const unsubscribePlayers = listenToAnswers(roomId, answer => {\n      const audio = sounds['correct'];\n      if (audio) {\n        audio.play();\n      }\n      setCorrectAnswer(`Đáp án: ${answer}`);\n    });\n\n    // No need to set state here; it's handled by useState initializer\n    return () => {\n      unsubscribePlayers();\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `bg-slate-800/80 backdrop-blur-sm rounded-xl border border-blue-400/30 shadow-2xl p-6 mb-4 w-full flex flex-col items-center`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: `text-white text-xl font-semibold text-center mb-4 max-w-[90%] ${isExpanded ? \"max-h-none\" : \"max-h-[120px] overflow-hidden\"}`,\n      children: currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.question\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `text-cyan-200 text-lg font-semibold text-center mb-4 max-w-[90%] ${isExpanded ? \"max-h-none\" : \"max-h-[120px] overflow-hidden\"}`,\n      children: correctAnswer\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `w-full h-[300px] flex items-center justify-center overflow-hidden cursor-pointer mb-4  min-h-[400px]`,\n      onClick: () => setIsModalOpen(true),\n      children: (_url$split$pop => {\n        const url = currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.imgUrl;\n        if (!url) return /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-white\",\n          children: \"No media\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 38\n        }, this);\n        const extension = ((_url$split$pop = url.split('.').pop()) === null || _url$split$pop === void 0 ? void 0 : _url$split$pop.toLowerCase()) || \"\";\n        if ([\"jpg\", \"jpeg\", \"png\", \"gif\", \"webp\"].includes(extension)) {\n          return /*#__PURE__*/_jsxDEV(\"img\", {\n            src: url,\n            alt: \"Question Visual\",\n            className: \"w-full h-full object-cover rounded-lg\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 32\n          }, this);\n        }\n        if ([\"mp3\", \"wav\", \"ogg\"].includes(extension)) {\n          return /*#__PURE__*/_jsxDEV(\"audio\", {\n            controls: true,\n            className: \"w-full h-full\",\n            children: [/*#__PURE__*/_jsxDEV(\"source\", {\n              src: url,\n              type: `audio/${extension}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 29\n            }, this), \"Your browser does not support the audio element.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 32\n          }, this);\n        }\n        if ([\"mp4\", \"webm\", \"ogg\"].includes(extension)) {\n          return /*#__PURE__*/_jsxDEV(\"video\", {\n            controls: true,\n            className: \"w-full h-full object-cover rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"source\", {\n              src: url,\n              type: `video/${extension}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 29\n            }, this), \"Your browser does not support the video tag.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 32\n          }, this);\n        }\n        return /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-white\",\n          children: \"Unsupported media type\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 28\n        }, this);\n      })()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 13\n    }, this), !isSpectator && /*#__PURE__*/_jsxDEV(PlayerAnswerInput, {\n      isHost: isHost,\n      question: currentQuestion\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 17\n    }, this), isModalOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-80 flex justify-center items-center z-50\",\n      onClick: () => setIsModalOpen(false),\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        src: currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.imgUrl,\n        alt: \"Full Size\",\n        className: \"max-w-full max-h-full rounded-xl\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 153,\n    columnNumber: 9\n  }, this);\n};\n\n// const Round1: React.FC<RoundBase> = ({ isHost }) => {\n//     return (\n//         <Play\n//             questionComponent={<QuestionBox question=\"Câu hỏi mẫu?\" imageUrl=\"https://a.travel-assets.com/findyours-php/viewfinder/images/res70/474000/474240-Left-Bank-Paris.jpg\" isHost={isHost} />}\n//             isHost={isHost}\n//         />\n//     );\n// }\n_s(PlayerQuestionBoxRoundTurn, \"w89reCKtct+F1rcJC8FL3eddCCQ=\", false, function () {\n  return [useSounds, useSearchParams, useTimeStart, usePlayer, useHost];\n});\n_c = PlayerQuestionBoxRoundTurn;\nexport default PlayerQuestionBoxRoundTurn;\nvar _c;\n$RefreshReg$(_c, \"PlayerQuestionBoxRoundTurn\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "listenToTimeStart", "listenToQuestions", "listenToAnswers", "listenToSound", "deletePath", "useSearchParams", "useHost", "useTimeStart", "usePlayer", "PlayerAnswerInput", "submitAnswer", "useSounds", "jsxDEV", "_jsxDEV", "PlayerQuestionBoxRoundTurn", "isHost", "isSpectator", "_s", "sounds", "searchParams", "roomId", "get", "currentQuestion", "setCurrentQuestion", "<PERSON><PERSON><PERSON><PERSON>", "setCorrectAnswer", "isExpanded", "setIsExpanded", "isModalOpen", "setIsModalOpen", "timeLeft", "playerAnswerTime", "startTimer", "setTimeLeft", "setAnswerList", "playerAnswerRef", "position", "animationKey", "setAnimationKey", "currentPlayerName", "currentPlayerAvatar", "currentAnswer", "console", "log", "current", "isInitialMount", "unsubscribe", "isInitialTimerMount", "prev", "unsubscribePlayers", "question", "type", "audio", "play", "answer", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_url$split$pop", "url", "imgUrl", "extension", "split", "pop", "toLowerCase", "includes", "src", "alt", "controls", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/layouts/RoundBase/Player/PlayerQuestionBoxRoundTurn.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react'\r\n\r\nimport { listenToTimeStart, listenToQuestions, listenToAnswers, listenToSound, deletePath } from '../../../services/firebaseServices';\r\nimport { useSearchParams } from 'react-router-dom';\r\nimport { useHost } from '../../../context/hostContext';\r\nimport { useTimeStart } from '../../../context/timeListenerContext';\r\nimport { usePlayer } from '../../../context/playerContext';\r\nimport PlayerAnswerInput from '../../../components/ui/PlayerAnswerInput';\r\nimport { Question } from '../../../shared/types';\r\nimport { submitAnswer } from '../../services';\r\nimport { useSounds } from '../../../context/soundContext';\r\nimport { set } from 'firebase/database';\r\n\r\n// interface QuestionBoxProps {\r\n//     question: string;\r\n//     imgUrl?: string;\r\n//     isHost?: boolean\r\n// }\r\n\r\ninterface RoundTurnProps {\r\n    isHost: boolean,\r\n    isSpectator?: boolean\r\n}\r\n\r\nconst PlayerQuestionBoxRoundTurn: React.FC<RoundTurnProps> = ({ isHost, isSpectator = false }) => {\r\n    const sounds = useSounds();\r\n    const [searchParams] = useSearchParams()\r\n    const roomId = searchParams.get(\"roomId\") || \"\"\r\n    const [currentQuestion, setCurrentQuestion] = useState<Question>()\r\n    const [correctAnswer, setCorrectAnswer] = useState<string>(\"\")\r\n    const [isExpanded, setIsExpanded] = useState(false);\r\n    const [isModalOpen, setIsModalOpen] = useState(false);\r\n    const { timeLeft, playerAnswerTime, startTimer, setTimeLeft } = useTimeStart();\r\n    const { setAnswerList, playerAnswerRef, position, animationKey, setAnimationKey, currentPlayerName, currentPlayerAvatar } = usePlayer()\r\n    const { currentAnswer } = useHost()\r\n    useEffect(() => {\r\n        console.log(\"playerAnswerRef.current\", playerAnswerRef.current);\r\n    }, [playerAnswerRef.current])\r\n    // useEffect(() => {\r\n    //     if (isInitialMount) return\r\n\r\n\r\n    //     startTimer(10);\r\n\r\n    //     return () => {\r\n    //         // Clean up timer\r\n    //     }\r\n\r\n\r\n    // }, []);\r\n    const isInitialMount = useRef(false)\r\n    useEffect(() => {\r\n        const unsubscribe = listenToTimeStart(roomId, async () => {\r\n\r\n\r\n            // Skip the timer setting on the first mount, but allow future calls to run\r\n            if (isInitialMount.current) {\r\n                isInitialMount.current = false;\r\n                return;\r\n            }\r\n            startTimer(10)\r\n            return () => {\r\n                unsubscribe();\r\n\r\n            };\r\n        })\r\n\r\n    }, [])\r\n\r\n    const isInitialTimerMount = useRef(true)\r\n    useEffect(() => {\r\n        console.log(\"timeLeft\", timeLeft);\r\n\r\n\r\n        if (isInitialTimerMount.current) {\r\n            isInitialTimerMount.current = false;\r\n            return;\r\n        }\r\n        if (timeLeft === 0) {\r\n\r\n            setAnimationKey((prev: number) => prev + 1);\r\n            if (!isHost && !isSpectator) {\r\n                console.log(\"playerAnswerRef.current\", playerAnswerRef.current);\r\n                console.log(\"position\", position);\r\n\r\n\r\n                // When timer runs out, do your clean up / game logic:\r\n                submitAnswer(roomId, playerAnswerRef.current, position, playerAnswerTime, currentPlayerName, currentPlayerAvatar)\r\n\r\n            }\r\n            // If you want to reset timer, call startTimer again here or leave stopped\r\n        }\r\n    }, [timeLeft]);\r\n    useEffect(() => {\r\n        const unsubscribePlayers = listenToQuestions(roomId, (question) => {\r\n            setCurrentQuestion(question)\r\n            console.log(\"isHost\", isHost);\r\n            \r\n            console.log(\"current correcr Answer\", currentAnswer);\r\n            \r\n            if (isHost) {\r\n                setCorrectAnswer(currentAnswer)\r\n            }\r\n            console.log(\"current question\", question)\r\n            setAnswerList(null)\r\n            if (!isHost) {\r\n                setCorrectAnswer(\"\")\r\n            }\r\n\r\n        });\r\n\r\n        // No need to set state here; it's handled by useState initializer\r\n        return () => {\r\n            unsubscribePlayers();\r\n        };\r\n    }, []);\r\n\r\n    useEffect(() => {\r\n        const unsubscribePlayers = listenToSound(roomId, async (type) => {\r\n\r\n            const audio = sounds[`${type}`];\r\n            if (audio) {\r\n                audio.play();\r\n            }\r\n            console.log(\"sound type\", type)\r\n            await deletePath(roomId, \"sound\")\r\n        });\r\n\r\n        // No need to set state here; it's handled by useState initializer\r\n        return () => {\r\n            unsubscribePlayers();\r\n        };\r\n    }, []);\r\n\r\n    useEffect(() => {\r\n\r\n        const unsubscribePlayers = listenToAnswers(roomId, (answer) => {\r\n            const audio = sounds['correct'];\r\n            if (audio) {\r\n                audio.play();\r\n            }\r\n            setCorrectAnswer(`Đáp án: ${answer}`)\r\n        });\r\n\r\n        // No need to set state here; it's handled by useState initializer\r\n        return () => {\r\n            unsubscribePlayers();\r\n\r\n        };\r\n    }, []);\r\n\r\n    return (\r\n        <div\r\n            className={`bg-slate-800/80 backdrop-blur-sm rounded-xl border border-blue-400/30 shadow-2xl p-6 mb-4 w-full flex flex-col items-center`}\r\n        >\r\n            {/* Question text */}\r\n            <div\r\n                className={`text-white text-xl font-semibold text-center mb-4 max-w-[90%] ${isExpanded ? \"max-h-none\" : \"max-h-[120px] overflow-hidden\"\r\n                    }`}\r\n            >\r\n                {currentQuestion?.question}\r\n            </div>\r\n\r\n            {/* Correct answer */}\r\n            <div\r\n                className={`text-cyan-200 text-lg font-semibold text-center mb-4 max-w-[90%] ${isExpanded ? \"max-h-none\" : \"max-h-[120px] overflow-hidden\"\r\n                    }`}\r\n            >\r\n                {correctAnswer}\r\n            </div>\r\n\r\n            {/* Media */}\r\n            <div\r\n                className={`w-full h-[300px] flex items-center justify-center overflow-hidden cursor-pointer mb-4  min-h-[400px]`}\r\n                onClick={() => setIsModalOpen(true)}\r\n            >\r\n                {(() => {\r\n                    const url = currentQuestion?.imgUrl;\r\n                    if (!url) return <p className=\"text-white\">No media</p>;\r\n\r\n                    const extension = url.split('.').pop()?.toLowerCase() || \"\";\r\n\r\n                    if ([\"jpg\", \"jpeg\", \"png\", \"gif\", \"webp\"].includes(extension)) {\r\n                        return <img src={url} alt=\"Question Visual\" className=\"w-full h-full object-cover rounded-lg\" />;\r\n                    }\r\n\r\n                    if ([\"mp3\", \"wav\", \"ogg\"].includes(extension)) {\r\n                        return <audio controls className=\"w-full h-full\">\r\n                            <source src={url} type={`audio/${extension}`} />\r\n                            Your browser does not support the audio element.\r\n                        </audio>;\r\n                    }\r\n\r\n                    if ([\"mp4\", \"webm\", \"ogg\"].includes(extension)) {\r\n                        return <video controls className=\"w-full h-full object-cover rounded-lg\">\r\n                            <source src={url} type={`video/${extension}`} />\r\n                            Your browser does not support the video tag.\r\n                        </video>;\r\n                    }\r\n\r\n                    return <p className=\"text-white\">Unsupported media type</p>;\r\n                })()}\r\n            </div>\r\n\r\n            {/* Answer input */}\r\n            {\r\n                !isSpectator &&\r\n                <PlayerAnswerInput\r\n                    isHost={isHost}\r\n                    question={currentQuestion}\r\n                />\r\n            }\r\n\r\n\r\n            {/* Modal for full-size image */}\r\n            {isModalOpen && (\r\n                <div className=\"fixed inset-0 bg-black bg-opacity-80 flex justify-center items-center z-50\"\r\n                    onClick={() => setIsModalOpen(false)}>\r\n                    <img src={currentQuestion?.imgUrl} alt=\"Full Size\" className=\"max-w-full max-h-full rounded-xl\" />\r\n                </div>\r\n            )}\r\n        </div>\r\n    );\r\n};\r\n\r\n\r\n// const Round1: React.FC<RoundBase> = ({ isHost }) => {\r\n//     return (\r\n//         <Play\r\n//             questionComponent={<QuestionBox question=\"Câu hỏi mẫu?\" imageUrl=\"https://a.travel-assets.com/findyours-php/viewfinder/images/res70/474000/474240-Left-Bank-Paris.jpg\" isHost={isHost} />}\r\n//             isHost={isHost}\r\n//         />\r\n//     );\r\n// }\r\n\r\nexport default PlayerQuestionBoxRoundTurn"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAE1D,SAASC,iBAAiB,EAAEC,iBAAiB,EAAEC,eAAe,EAAEC,aAAa,EAAEC,UAAU,QAAQ,oCAAoC;AACrI,SAASC,eAAe,QAAQ,kBAAkB;AAClD,SAASC,OAAO,QAAQ,8BAA8B;AACtD,SAASC,YAAY,QAAQ,sCAAsC;AACnE,SAASC,SAAS,QAAQ,gCAAgC;AAC1D,OAAOC,iBAAiB,MAAM,0CAA0C;AAExE,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,SAAS,QAAQ,+BAA+B;;AAGzD;AACA;AACA;AACA;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAOA,MAAMC,0BAAoD,GAAGA,CAAC;EAAEC,MAAM;EAAEC,WAAW,GAAG;AAAM,CAAC,KAAK;EAAAC,EAAA;EAC9F,MAAMC,MAAM,GAAGP,SAAS,CAAC,CAAC;EAC1B,MAAM,CAACQ,YAAY,CAAC,GAAGd,eAAe,CAAC,CAAC;EACxC,MAAMe,MAAM,GAAGD,YAAY,CAACE,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE;EAC/C,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAG1B,QAAQ,CAAW,CAAC;EAClE,MAAM,CAAC2B,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAS,EAAE,CAAC;EAC9D,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC+B,WAAW,EAAEC,cAAc,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM;IAAEiC,QAAQ;IAAEC,gBAAgB;IAAEC,UAAU;IAAEC;EAAY,CAAC,GAAG1B,YAAY,CAAC,CAAC;EAC9E,MAAM;IAAE2B,aAAa;IAAEC,eAAe;IAAEC,QAAQ;IAAEC,YAAY;IAAEC,eAAe;IAAEC,iBAAiB;IAAEC;EAAoB,CAAC,GAAGhC,SAAS,CAAC,CAAC;EACvI,MAAM;IAAEiC;EAAc,CAAC,GAAGnC,OAAO,CAAC,CAAC;EACnCR,SAAS,CAAC,MAAM;IACZ4C,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAER,eAAe,CAACS,OAAO,CAAC;EACnE,CAAC,EAAE,CAACT,eAAe,CAACS,OAAO,CAAC,CAAC;EAC7B;EACA;;EAGA;;EAEA;EACA;EACA;;EAGA;EACA,MAAMC,cAAc,GAAG9C,MAAM,CAAC,KAAK,CAAC;EACpCD,SAAS,CAAC,MAAM;IACZ,MAAMgD,WAAW,GAAG9C,iBAAiB,CAACoB,MAAM,EAAE,YAAY;MAGtD;MACA,IAAIyB,cAAc,CAACD,OAAO,EAAE;QACxBC,cAAc,CAACD,OAAO,GAAG,KAAK;QAC9B;MACJ;MACAZ,UAAU,CAAC,EAAE,CAAC;MACd,OAAO,MAAM;QACTc,WAAW,CAAC,CAAC;MAEjB,CAAC;IACL,CAAC,CAAC;EAEN,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,mBAAmB,GAAGhD,MAAM,CAAC,IAAI,CAAC;EACxCD,SAAS,CAAC,MAAM;IACZ4C,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEb,QAAQ,CAAC;IAGjC,IAAIiB,mBAAmB,CAACH,OAAO,EAAE;MAC7BG,mBAAmB,CAACH,OAAO,GAAG,KAAK;MACnC;IACJ;IACA,IAAId,QAAQ,KAAK,CAAC,EAAE;MAEhBQ,eAAe,CAAEU,IAAY,IAAKA,IAAI,GAAG,CAAC,CAAC;MAC3C,IAAI,CAACjC,MAAM,IAAI,CAACC,WAAW,EAAE;QACzB0B,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAER,eAAe,CAACS,OAAO,CAAC;QAC/DF,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEP,QAAQ,CAAC;;QAGjC;QACA1B,YAAY,CAACU,MAAM,EAAEe,eAAe,CAACS,OAAO,EAAER,QAAQ,EAAEL,gBAAgB,EAAEQ,iBAAiB,EAAEC,mBAAmB,CAAC;MAErH;MACA;IACJ;EACJ,CAAC,EAAE,CAACV,QAAQ,CAAC,CAAC;EACdhC,SAAS,CAAC,MAAM;IACZ,MAAMmD,kBAAkB,GAAGhD,iBAAiB,CAACmB,MAAM,EAAG8B,QAAQ,IAAK;MAC/D3B,kBAAkB,CAAC2B,QAAQ,CAAC;MAC5BR,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAE5B,MAAM,CAAC;MAE7B2B,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEF,aAAa,CAAC;MAEpD,IAAI1B,MAAM,EAAE;QACRU,gBAAgB,CAACgB,aAAa,CAAC;MACnC;MACAC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEO,QAAQ,CAAC;MACzChB,aAAa,CAAC,IAAI,CAAC;MACnB,IAAI,CAACnB,MAAM,EAAE;QACTU,gBAAgB,CAAC,EAAE,CAAC;MACxB;IAEJ,CAAC,CAAC;;IAEF;IACA,OAAO,MAAM;MACTwB,kBAAkB,CAAC,CAAC;IACxB,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAENnD,SAAS,CAAC,MAAM;IACZ,MAAMmD,kBAAkB,GAAG9C,aAAa,CAACiB,MAAM,EAAE,MAAO+B,IAAI,IAAK;MAE7D,MAAMC,KAAK,GAAGlC,MAAM,CAAC,GAAGiC,IAAI,EAAE,CAAC;MAC/B,IAAIC,KAAK,EAAE;QACPA,KAAK,CAACC,IAAI,CAAC,CAAC;MAChB;MACAX,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEQ,IAAI,CAAC;MAC/B,MAAM/C,UAAU,CAACgB,MAAM,EAAE,OAAO,CAAC;IACrC,CAAC,CAAC;;IAEF;IACA,OAAO,MAAM;MACT6B,kBAAkB,CAAC,CAAC;IACxB,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAENnD,SAAS,CAAC,MAAM;IAEZ,MAAMmD,kBAAkB,GAAG/C,eAAe,CAACkB,MAAM,EAAGkC,MAAM,IAAK;MAC3D,MAAMF,KAAK,GAAGlC,MAAM,CAAC,SAAS,CAAC;MAC/B,IAAIkC,KAAK,EAAE;QACPA,KAAK,CAACC,IAAI,CAAC,CAAC;MAChB;MACA5B,gBAAgB,CAAC,WAAW6B,MAAM,EAAE,CAAC;IACzC,CAAC,CAAC;;IAEF;IACA,OAAO,MAAM;MACTL,kBAAkB,CAAC,CAAC;IAExB,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAEN,oBACIpC,OAAA;IACI0C,SAAS,EAAE,6HAA8H;IAAAC,QAAA,gBAGzI3C,OAAA;MACI0C,SAAS,EAAE,iEAAiE7B,UAAU,GAAG,YAAY,GAAG,+BAA+B,EAChI;MAAA8B,QAAA,EAENlC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE4B;IAAQ;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eAGN/C,OAAA;MACI0C,SAAS,EAAE,oEAAoE7B,UAAU,GAAG,YAAY,GAAG,+BAA+B,EACnI;MAAA8B,QAAA,EAENhC;IAAa;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC,eAGN/C,OAAA;MACI0C,SAAS,EAAE,sGAAuG;MAClHM,OAAO,EAAEA,CAAA,KAAMhC,cAAc,CAAC,IAAI,CAAE;MAAA2B,QAAA,EAEnC,CAACM,cAAA,IAAM;QACJ,MAAMC,GAAG,GAAGzC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE0C,MAAM;QACnC,IAAI,CAACD,GAAG,EAAE,oBAAOlD,OAAA;UAAG0C,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;QAEvD,MAAMK,SAAS,GAAG,EAAAH,cAAA,GAAAC,GAAG,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,cAAAL,cAAA,uBAApBA,cAAA,CAAsBM,WAAW,CAAC,CAAC,KAAI,EAAE;QAE3D,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAACC,QAAQ,CAACJ,SAAS,CAAC,EAAE;UAC3D,oBAAOpD,OAAA;YAAKyD,GAAG,EAAEP,GAAI;YAACQ,GAAG,EAAC,iBAAiB;YAAChB,SAAS,EAAC;UAAuC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QACpG;QAEA,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAACS,QAAQ,CAACJ,SAAS,CAAC,EAAE;UAC3C,oBAAOpD,OAAA;YAAO2D,QAAQ;YAACjB,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5C3C,OAAA;cAAQyD,GAAG,EAAEP,GAAI;cAACZ,IAAI,EAAE,SAASc,SAAS;YAAG;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,oDAEpD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QACZ;QAEA,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,CAACS,QAAQ,CAACJ,SAAS,CAAC,EAAE;UAC5C,oBAAOpD,OAAA;YAAO2D,QAAQ;YAACjB,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpE3C,OAAA;cAAQyD,GAAG,EAAEP,GAAI;cAACZ,IAAI,EAAE,SAASc,SAAS;YAAG;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gDAEpD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QACZ;QAEA,oBAAO/C,OAAA;UAAG0C,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAC/D,CAAC,EAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAIF,CAAC5C,WAAW,iBACZH,OAAA,CAACJ,iBAAiB;MACdM,MAAM,EAAEA,MAAO;MACfmC,QAAQ,EAAE5B;IAAgB;MAAAmC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC,EAKLhC,WAAW,iBACRf,OAAA;MAAK0C,SAAS,EAAC,4EAA4E;MACvFM,OAAO,EAAEA,CAAA,KAAMhC,cAAc,CAAC,KAAK,CAAE;MAAA2B,QAAA,eACrC3C,OAAA;QAAKyD,GAAG,EAAEhD,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE0C,MAAO;QAACO,GAAG,EAAC,WAAW;QAAChB,SAAS,EAAC;MAAkC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjG,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA3C,EAAA,CAjNMH,0BAAoD;EAAA,QACvCH,SAAS,EACDN,eAAe,EAM0BE,YAAY,EACgDC,SAAS,EAC3GF,OAAO;AAAA;AAAAmE,EAAA,GAV/B3D,0BAAoD;AAmN1D,eAAeA,0BAA0B;AAAA,IAAA2D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}