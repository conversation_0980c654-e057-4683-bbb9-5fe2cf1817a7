{"ast": null, "code": "import React from 'react';\nimport { notifyManager } from '../core/notifyManager';\nimport { useQueryErrorResetBoundary } from './QueryErrorResetBoundary';\nimport { useQueryClient } from './QueryClientProvider';\nimport { shouldThrowError } from './utils';\nexport function useBaseQuery(options, Observer) {\n  var mountedRef = React.useRef(false);\n  var _React$useState = React.useState(0),\n    forceUpdate = _React$useState[1];\n  var queryClient = useQueryClient();\n  var errorResetBoundary = useQueryErrorResetBoundary();\n  var defaultedOptions = queryClient.defaultQueryObserverOptions(options); // Make sure results are optimistically set in fetching state before subscribing or updating options\n\n  defaultedOptions.optimisticResults = true; // Include callbacks in batch renders\n\n  if (defaultedOptions.onError) {\n    defaultedOptions.onError = notifyManager.batchCalls(defaultedOptions.onError);\n  }\n  if (defaultedOptions.onSuccess) {\n    defaultedOptions.onSuccess = notifyManager.batchCalls(defaultedOptions.onSuccess);\n  }\n  if (defaultedOptions.onSettled) {\n    defaultedOptions.onSettled = notifyManager.batchCalls(defaultedOptions.onSettled);\n  }\n  if (defaultedOptions.suspense) {\n    // Always set stale time when using suspense to prevent\n    // fetching again when directly mounting after suspending\n    if (typeof defaultedOptions.staleTime !== 'number') {\n      defaultedOptions.staleTime = 1000;\n    } // Set cache time to 1 if the option has been set to 0\n    // when using suspense to prevent infinite loop of fetches\n\n    if (defaultedOptions.cacheTime === 0) {\n      defaultedOptions.cacheTime = 1;\n    }\n  }\n  if (defaultedOptions.suspense || defaultedOptions.useErrorBoundary) {\n    // Prevent retrying failed query if the error boundary has not been reset yet\n    if (!errorResetBoundary.isReset()) {\n      defaultedOptions.retryOnMount = false;\n    }\n  }\n  var _React$useState2 = React.useState(function () {\n      return new Observer(queryClient, defaultedOptions);\n    }),\n    observer = _React$useState2[0];\n  var result = observer.getOptimisticResult(defaultedOptions);\n  React.useEffect(function () {\n    mountedRef.current = true;\n    errorResetBoundary.clearReset();\n    var unsubscribe = observer.subscribe(notifyManager.batchCalls(function () {\n      if (mountedRef.current) {\n        forceUpdate(function (x) {\n          return x + 1;\n        });\n      }\n    })); // Update result to make sure we did not miss any query updates\n    // between creating the observer and subscribing to it.\n\n    observer.updateResult();\n    return function () {\n      mountedRef.current = false;\n      unsubscribe();\n    };\n  }, [errorResetBoundary, observer]);\n  React.useEffect(function () {\n    // Do not notify on updates because of changes in the options because\n    // these changes should already be reflected in the optimistic result.\n    observer.setOptions(defaultedOptions, {\n      listeners: false\n    });\n  }, [defaultedOptions, observer]); // Handle suspense\n\n  if (defaultedOptions.suspense && result.isLoading) {\n    throw observer.fetchOptimistic(defaultedOptions).then(function (_ref) {\n      var data = _ref.data;\n      defaultedOptions.onSuccess == null ? void 0 : defaultedOptions.onSuccess(data);\n      defaultedOptions.onSettled == null ? void 0 : defaultedOptions.onSettled(data, null);\n    }).catch(function (error) {\n      errorResetBoundary.clearReset();\n      defaultedOptions.onError == null ? void 0 : defaultedOptions.onError(error);\n      defaultedOptions.onSettled == null ? void 0 : defaultedOptions.onSettled(undefined, error);\n    });\n  } // Handle error boundary\n\n  if (result.isError && !errorResetBoundary.isReset() && !result.isFetching && shouldThrowError(defaultedOptions.suspense, defaultedOptions.useErrorBoundary, [result.error, observer.getCurrentQuery()])) {\n    throw result.error;\n  } // Handle result property usage tracking\n\n  if (defaultedOptions.notifyOnChangeProps === 'tracked') {\n    result = observer.trackResult(result, defaultedOptions);\n  }\n  return result;\n}", "map": {"version": 3, "names": ["React", "notify<PERSON><PERSON>ger", "useQueryErrorResetBoundary", "useQueryClient", "shouldThrowError", "useBaseQuery", "options", "Observer", "mountedRef", "useRef", "_React$useState", "useState", "forceUpdate", "queryClient", "errorResetBoundary", "defaultedOptions", "defaultQueryObserverOptions", "optimisticResults", "onError", "batchCalls", "onSuccess", "onSettled", "suspense", "staleTime", "cacheTime", "useErrorBoundary", "isReset", "retryOnMount", "_React$useState2", "observer", "result", "getOptimisticResult", "useEffect", "current", "clear<PERSON><PERSON>t", "unsubscribe", "subscribe", "x", "updateResult", "setOptions", "listeners", "isLoading", "fetchOptimistic", "then", "_ref", "data", "catch", "error", "undefined", "isError", "isFetching", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "notifyOnChangeProps", "trackResult"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/node_modules/react-query/es/react/useBaseQuery.js"], "sourcesContent": ["import React from 'react';\nimport { notifyManager } from '../core/notifyManager';\nimport { useQueryErrorResetBoundary } from './QueryErrorResetBoundary';\nimport { useQueryClient } from './QueryClientProvider';\nimport { shouldThrowError } from './utils';\nexport function useBaseQuery(options, Observer) {\n  var mountedRef = React.useRef(false);\n\n  var _React$useState = React.useState(0),\n      forceUpdate = _React$useState[1];\n\n  var queryClient = useQueryClient();\n  var errorResetBoundary = useQueryErrorResetBoundary();\n  var defaultedOptions = queryClient.defaultQueryObserverOptions(options); // Make sure results are optimistically set in fetching state before subscribing or updating options\n\n  defaultedOptions.optimisticResults = true; // Include callbacks in batch renders\n\n  if (defaultedOptions.onError) {\n    defaultedOptions.onError = notifyManager.batchCalls(defaultedOptions.onError);\n  }\n\n  if (defaultedOptions.onSuccess) {\n    defaultedOptions.onSuccess = notifyManager.batchCalls(defaultedOptions.onSuccess);\n  }\n\n  if (defaultedOptions.onSettled) {\n    defaultedOptions.onSettled = notifyManager.batchCalls(defaultedOptions.onSettled);\n  }\n\n  if (defaultedOptions.suspense) {\n    // Always set stale time when using suspense to prevent\n    // fetching again when directly mounting after suspending\n    if (typeof defaultedOptions.staleTime !== 'number') {\n      defaultedOptions.staleTime = 1000;\n    } // Set cache time to 1 if the option has been set to 0\n    // when using suspense to prevent infinite loop of fetches\n\n\n    if (defaultedOptions.cacheTime === 0) {\n      defaultedOptions.cacheTime = 1;\n    }\n  }\n\n  if (defaultedOptions.suspense || defaultedOptions.useErrorBoundary) {\n    // Prevent retrying failed query if the error boundary has not been reset yet\n    if (!errorResetBoundary.isReset()) {\n      defaultedOptions.retryOnMount = false;\n    }\n  }\n\n  var _React$useState2 = React.useState(function () {\n    return new Observer(queryClient, defaultedOptions);\n  }),\n      observer = _React$useState2[0];\n\n  var result = observer.getOptimisticResult(defaultedOptions);\n  React.useEffect(function () {\n    mountedRef.current = true;\n    errorResetBoundary.clearReset();\n    var unsubscribe = observer.subscribe(notifyManager.batchCalls(function () {\n      if (mountedRef.current) {\n        forceUpdate(function (x) {\n          return x + 1;\n        });\n      }\n    })); // Update result to make sure we did not miss any query updates\n    // between creating the observer and subscribing to it.\n\n    observer.updateResult();\n    return function () {\n      mountedRef.current = false;\n      unsubscribe();\n    };\n  }, [errorResetBoundary, observer]);\n  React.useEffect(function () {\n    // Do not notify on updates because of changes in the options because\n    // these changes should already be reflected in the optimistic result.\n    observer.setOptions(defaultedOptions, {\n      listeners: false\n    });\n  }, [defaultedOptions, observer]); // Handle suspense\n\n  if (defaultedOptions.suspense && result.isLoading) {\n    throw observer.fetchOptimistic(defaultedOptions).then(function (_ref) {\n      var data = _ref.data;\n      defaultedOptions.onSuccess == null ? void 0 : defaultedOptions.onSuccess(data);\n      defaultedOptions.onSettled == null ? void 0 : defaultedOptions.onSettled(data, null);\n    }).catch(function (error) {\n      errorResetBoundary.clearReset();\n      defaultedOptions.onError == null ? void 0 : defaultedOptions.onError(error);\n      defaultedOptions.onSettled == null ? void 0 : defaultedOptions.onSettled(undefined, error);\n    });\n  } // Handle error boundary\n\n\n  if (result.isError && !errorResetBoundary.isReset() && !result.isFetching && shouldThrowError(defaultedOptions.suspense, defaultedOptions.useErrorBoundary, [result.error, observer.getCurrentQuery()])) {\n    throw result.error;\n  } // Handle result property usage tracking\n\n\n  if (defaultedOptions.notifyOnChangeProps === 'tracked') {\n    result = observer.trackResult(result, defaultedOptions);\n  }\n\n  return result;\n}"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,0BAA0B,QAAQ,2BAA2B;AACtE,SAASC,cAAc,QAAQ,uBAAuB;AACtD,SAASC,gBAAgB,QAAQ,SAAS;AAC1C,OAAO,SAASC,YAAYA,CAACC,OAAO,EAAEC,QAAQ,EAAE;EAC9C,IAAIC,UAAU,GAAGR,KAAK,CAACS,MAAM,CAAC,KAAK,CAAC;EAEpC,IAAIC,eAAe,GAAGV,KAAK,CAACW,QAAQ,CAAC,CAAC,CAAC;IACnCC,WAAW,GAAGF,eAAe,CAAC,CAAC,CAAC;EAEpC,IAAIG,WAAW,GAAGV,cAAc,CAAC,CAAC;EAClC,IAAIW,kBAAkB,GAAGZ,0BAA0B,CAAC,CAAC;EACrD,IAAIa,gBAAgB,GAAGF,WAAW,CAACG,2BAA2B,CAACV,OAAO,CAAC,CAAC,CAAC;;EAEzES,gBAAgB,CAACE,iBAAiB,GAAG,IAAI,CAAC,CAAC;;EAE3C,IAAIF,gBAAgB,CAACG,OAAO,EAAE;IAC5BH,gBAAgB,CAACG,OAAO,GAAGjB,aAAa,CAACkB,UAAU,CAACJ,gBAAgB,CAACG,OAAO,CAAC;EAC/E;EAEA,IAAIH,gBAAgB,CAACK,SAAS,EAAE;IAC9BL,gBAAgB,CAACK,SAAS,GAAGnB,aAAa,CAACkB,UAAU,CAACJ,gBAAgB,CAACK,SAAS,CAAC;EACnF;EAEA,IAAIL,gBAAgB,CAACM,SAAS,EAAE;IAC9BN,gBAAgB,CAACM,SAAS,GAAGpB,aAAa,CAACkB,UAAU,CAACJ,gBAAgB,CAACM,SAAS,CAAC;EACnF;EAEA,IAAIN,gBAAgB,CAACO,QAAQ,EAAE;IAC7B;IACA;IACA,IAAI,OAAOP,gBAAgB,CAACQ,SAAS,KAAK,QAAQ,EAAE;MAClDR,gBAAgB,CAACQ,SAAS,GAAG,IAAI;IACnC,CAAC,CAAC;IACF;;IAGA,IAAIR,gBAAgB,CAACS,SAAS,KAAK,CAAC,EAAE;MACpCT,gBAAgB,CAACS,SAAS,GAAG,CAAC;IAChC;EACF;EAEA,IAAIT,gBAAgB,CAACO,QAAQ,IAAIP,gBAAgB,CAACU,gBAAgB,EAAE;IAClE;IACA,IAAI,CAACX,kBAAkB,CAACY,OAAO,CAAC,CAAC,EAAE;MACjCX,gBAAgB,CAACY,YAAY,GAAG,KAAK;IACvC;EACF;EAEA,IAAIC,gBAAgB,GAAG5B,KAAK,CAACW,QAAQ,CAAC,YAAY;MAChD,OAAO,IAAIJ,QAAQ,CAACM,WAAW,EAAEE,gBAAgB,CAAC;IACpD,CAAC,CAAC;IACEc,QAAQ,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EAElC,IAAIE,MAAM,GAAGD,QAAQ,CAACE,mBAAmB,CAAChB,gBAAgB,CAAC;EAC3Df,KAAK,CAACgC,SAAS,CAAC,YAAY;IAC1BxB,UAAU,CAACyB,OAAO,GAAG,IAAI;IACzBnB,kBAAkB,CAACoB,UAAU,CAAC,CAAC;IAC/B,IAAIC,WAAW,GAAGN,QAAQ,CAACO,SAAS,CAACnC,aAAa,CAACkB,UAAU,CAAC,YAAY;MACxE,IAAIX,UAAU,CAACyB,OAAO,EAAE;QACtBrB,WAAW,CAAC,UAAUyB,CAAC,EAAE;UACvB,OAAOA,CAAC,GAAG,CAAC;QACd,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,CAAC,CAAC,CAAC;IACL;;IAEAR,QAAQ,CAACS,YAAY,CAAC,CAAC;IACvB,OAAO,YAAY;MACjB9B,UAAU,CAACyB,OAAO,GAAG,KAAK;MAC1BE,WAAW,CAAC,CAAC;IACf,CAAC;EACH,CAAC,EAAE,CAACrB,kBAAkB,EAAEe,QAAQ,CAAC,CAAC;EAClC7B,KAAK,CAACgC,SAAS,CAAC,YAAY;IAC1B;IACA;IACAH,QAAQ,CAACU,UAAU,CAACxB,gBAAgB,EAAE;MACpCyB,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC,EAAE,CAACzB,gBAAgB,EAAEc,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAElC,IAAId,gBAAgB,CAACO,QAAQ,IAAIQ,MAAM,CAACW,SAAS,EAAE;IACjD,MAAMZ,QAAQ,CAACa,eAAe,CAAC3B,gBAAgB,CAAC,CAAC4B,IAAI,CAAC,UAAUC,IAAI,EAAE;MACpE,IAAIC,IAAI,GAAGD,IAAI,CAACC,IAAI;MACpB9B,gBAAgB,CAACK,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGL,gBAAgB,CAACK,SAAS,CAACyB,IAAI,CAAC;MAC9E9B,gBAAgB,CAACM,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGN,gBAAgB,CAACM,SAAS,CAACwB,IAAI,EAAE,IAAI,CAAC;IACtF,CAAC,CAAC,CAACC,KAAK,CAAC,UAAUC,KAAK,EAAE;MACxBjC,kBAAkB,CAACoB,UAAU,CAAC,CAAC;MAC/BnB,gBAAgB,CAACG,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGH,gBAAgB,CAACG,OAAO,CAAC6B,KAAK,CAAC;MAC3EhC,gBAAgB,CAACM,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGN,gBAAgB,CAACM,SAAS,CAAC2B,SAAS,EAAED,KAAK,CAAC;IAC5F,CAAC,CAAC;EACJ,CAAC,CAAC;;EAGF,IAAIjB,MAAM,CAACmB,OAAO,IAAI,CAACnC,kBAAkB,CAACY,OAAO,CAAC,CAAC,IAAI,CAACI,MAAM,CAACoB,UAAU,IAAI9C,gBAAgB,CAACW,gBAAgB,CAACO,QAAQ,EAAEP,gBAAgB,CAACU,gBAAgB,EAAE,CAACK,MAAM,CAACiB,KAAK,EAAElB,QAAQ,CAACsB,eAAe,CAAC,CAAC,CAAC,CAAC,EAAE;IACvM,MAAMrB,MAAM,CAACiB,KAAK;EACpB,CAAC,CAAC;;EAGF,IAAIhC,gBAAgB,CAACqC,mBAAmB,KAAK,SAAS,EAAE;IACtDtB,MAAM,GAAGD,QAAQ,CAACwB,WAAW,CAACvB,MAAM,EAAEf,gBAAgB,CAAC;EACzD;EAEA,OAAOe,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}