{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\New HTM\\\\htm_fe\\\\src\\\\shared\\\\components\\\\ui\\\\Button\\\\Button.tsx\";\n// Button component\nimport React, { forwardRef } from 'react';\nimport { getButtonClasses, loadingSpinnerClasses } from './Button.variants';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoadingSpinner = () => /*#__PURE__*/_jsxDEV(\"svg\", {\n  className: loadingSpinnerClasses,\n  xmlns: \"http://www.w3.org/2000/svg\",\n  fill: \"none\",\n  viewBox: \"0 0 24 24\",\n  children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n    className: \"opacity-25\",\n    cx: \"12\",\n    cy: \"12\",\n    r: \"10\",\n    stroke: \"currentColor\",\n    strokeWidth: \"4\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    className: \"opacity-75\",\n    fill: \"currentColor\",\n    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 7,\n  columnNumber: 3\n}, this);\n_c = LoadingSpinner;\nexport const Button = /*#__PURE__*/forwardRef(_c2 = ({\n  children,\n  variant = 'primary',\n  size = 'md',\n  isLoading = false,\n  isDisabled = false,\n  leftIcon,\n  rightIcon,\n  fullWidth = false,\n  className,\n  type = 'button',\n  ...props\n}, ref) => {\n  const buttonClasses = getButtonClasses(variant, size, fullWidth, isDisabled || isLoading, className);\n  return /*#__PURE__*/_jsxDEV(\"button\", {\n    ref: ref,\n    type: type,\n    className: buttonClasses,\n    disabled: isDisabled || isLoading,\n    ...props,\n    children: [isLoading && /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 23\n    }, this), !isLoading && leftIcon && /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"mr-2\",\n      children: leftIcon\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 36\n    }, this), children, !isLoading && rightIcon && /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"ml-2\",\n      children: rightIcon\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 37\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 7\n  }, this);\n});\n_c3 = Button;\nButton.displayName = 'Button';\nexport default Button;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"LoadingSpinner\");\n$RefreshReg$(_c2, \"Button$forwardRef\");\n$RefreshReg$(_c3, \"Button\");", "map": {"version": 3, "names": ["React", "forwardRef", "getButtonClasses", "loadingSpinnerClasses", "jsxDEV", "_jsxDEV", "LoadingSpinner", "className", "xmlns", "fill", "viewBox", "children", "cx", "cy", "r", "stroke", "strokeWidth", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "d", "_c", "<PERSON><PERSON>", "_c2", "variant", "size", "isLoading", "isDisabled", "leftIcon", "rightIcon", "fullWidth", "type", "props", "ref", "buttonClasses", "disabled", "_c3", "displayName", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/shared/components/ui/Button/Button.tsx"], "sourcesContent": ["// Button component\r\nimport React, { forwardRef } from 'react';\r\nimport { ButtonProps } from './Button.types';\r\nimport { getButtonClasses, loadingSpinnerClasses } from './Button.variants';\r\n\r\nconst LoadingSpinner = () => (\r\n  <svg className={loadingSpinnerClasses} xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n    <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n    <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n  </svg>\r\n);\r\n\r\nexport const Button = forwardRef<HTMLButtonElement, ButtonProps>(\r\n  (\r\n    {\r\n      children,\r\n      variant = 'primary',\r\n      size = 'md',\r\n      isLoading = false,\r\n      isDisabled = false,\r\n      leftIcon,\r\n      rightIcon,\r\n      fullWidth = false,\r\n      className,\r\n      type = 'button',\r\n      ...props\r\n    },\r\n    ref\r\n  ) => {\r\n    const buttonClasses = getButtonClasses(variant, size, fullWidth, isDisabled || isLoading, className);\r\n\r\n    return (\r\n      <button\r\n        ref={ref}\r\n        type={type}\r\n        className={buttonClasses}\r\n        disabled={isDisabled || isLoading}\r\n        {...props}\r\n      >\r\n        {isLoading && <LoadingSpinner />}\r\n        {!isLoading && leftIcon && <span className=\"mr-2\">{leftIcon}</span>}\r\n        {children}\r\n        {!isLoading && rightIcon && <span className=\"ml-2\">{rightIcon}</span>}\r\n      </button>\r\n    );\r\n  }\r\n);\r\n\r\nButton.displayName = 'Button';\r\n\r\nexport default Button;\r\n"], "mappings": ";AAAA;AACA,OAAOA,KAAK,IAAIC,UAAU,QAAQ,OAAO;AAEzC,SAASC,gBAAgB,EAAEC,qBAAqB,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5E,MAAMC,cAAc,GAAGA,CAAA,kBACrBD,OAAA;EAAKE,SAAS,EAAEJ,qBAAsB;EAACK,KAAK,EAAC,4BAA4B;EAACC,IAAI,EAAC,MAAM;EAACC,OAAO,EAAC,WAAW;EAAAC,QAAA,gBACvGN,OAAA;IAAQE,SAAS,EAAC,YAAY;IAACK,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,CAAC,EAAC,IAAI;IAACC,MAAM,EAAC,cAAc;IAACC,WAAW,EAAC;EAAG;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAS,CAAC,eACrGf,OAAA;IAAME,SAAS,EAAC,YAAY;IAACE,IAAI,EAAC,cAAc;IAACY,CAAC,EAAC;EAAiH;IAAAJ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAO,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACzK,CACN;AAACE,EAAA,GALIhB,cAAc;AAOpB,OAAO,MAAMiB,MAAM,gBAAGtB,UAAU,CAAAuB,GAAA,GAC9BA,CACE;EACEb,QAAQ;EACRc,OAAO,GAAG,SAAS;EACnBC,IAAI,GAAG,IAAI;EACXC,SAAS,GAAG,KAAK;EACjBC,UAAU,GAAG,KAAK;EAClBC,QAAQ;EACRC,SAAS;EACTC,SAAS,GAAG,KAAK;EACjBxB,SAAS;EACTyB,IAAI,GAAG,QAAQ;EACf,GAAGC;AACL,CAAC,EACDC,GAAG,KACA;EACH,MAAMC,aAAa,GAAGjC,gBAAgB,CAACuB,OAAO,EAAEC,IAAI,EAAEK,SAAS,EAAEH,UAAU,IAAID,SAAS,EAAEpB,SAAS,CAAC;EAEpG,oBACEF,OAAA;IACE6B,GAAG,EAAEA,GAAI;IACTF,IAAI,EAAEA,IAAK;IACXzB,SAAS,EAAE4B,aAAc;IACzBC,QAAQ,EAAER,UAAU,IAAID,SAAU;IAAA,GAC9BM,KAAK;IAAAtB,QAAA,GAERgB,SAAS,iBAAItB,OAAA,CAACC,cAAc;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAC/B,CAACO,SAAS,IAAIE,QAAQ,iBAAIxB,OAAA;MAAME,SAAS,EAAC,MAAM;MAAAI,QAAA,EAAEkB;IAAQ;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,EAClET,QAAQ,EACR,CAACgB,SAAS,IAAIG,SAAS,iBAAIzB,OAAA;MAAME,SAAS,EAAC,MAAM;MAAAI,QAAA,EAAEmB;IAAS;MAAAb,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC/D,CAAC;AAEb,CACF,CAAC;AAACiB,GAAA,GAlCWd,MAAM;AAoCnBA,MAAM,CAACe,WAAW,GAAG,QAAQ;AAE7B,eAAef,MAAM;AAAC,IAAAD,EAAA,EAAAE,GAAA,EAAAa,GAAA;AAAAE,YAAA,CAAAjB,EAAA;AAAAiB,YAAA,CAAAf,GAAA;AAAAe,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}