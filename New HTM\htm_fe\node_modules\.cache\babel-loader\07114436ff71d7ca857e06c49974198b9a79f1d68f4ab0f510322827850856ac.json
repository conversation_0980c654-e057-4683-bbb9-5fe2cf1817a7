{"ast": null, "code": "// Authentication Redux slice\nimport { createSlice, createAsyncThunk } from '@reduxjs/toolkit';\n// Initial state\nconst initialState = {\n  user: null,\n  profile: null,\n  isAuthenticated: false,\n  isLoading: false,\n  error: null,\n  accessToken: localStorage.getItem('accessToken'),\n  tokenExpiry: null\n};\n\n// Async thunks (will be implemented with actual API calls later)\nexport const loginUser = createAsyncThunk('auth/loginUser', async (credentials, {\n  rejectWithValue\n}) => {\n  try {\n    // TODO: Replace with actual API call\n    const response = await fetch('/api/auth/login', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify(credentials)\n    });\n    if (!response.ok) {\n      throw new Error('Login failed');\n    }\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    return rejectWithValue(error.message);\n  }\n});\nexport const authenticateUser = createAsyncThunk('auth/authenticate', async (credentials, {\n  rejectWithValue\n}) => {\n  try {\n    const response = await fetch('/api/auth/token', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify(credentials)\n    });\n    if (!response.ok) {\n      throw new Error('Authentication failed');\n    }\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    return rejectWithValue(error.message);\n  }\n});\nexport const refreshAccessToken = createAsyncThunk('auth/refreshAccessToken', async (_, {\n  getState,\n  rejectWithValue\n}) => {\n  try {\n    const state = getState();\n    const refreshToken = state.auth.refreshToken;\n    if (!refreshToken) {\n      throw new Error('No refresh token available');\n    }\n\n    // TODO: Replace with actual API call\n    const response = await fetch('/api/auth/refresh', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify({\n        refreshToken\n      })\n    });\n    if (!response.ok) {\n      throw new Error('Token refresh failed');\n    }\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    return rejectWithValue(error.message);\n  }\n});\nexport const logoutUser = createAsyncThunk('auth/logoutUser', async (_, {\n  rejectWithValue\n}) => {\n  try {\n    // TODO: Replace with actual API call\n    await fetch('/api/auth/logout', {\n      method: 'POST',\n      credentials: 'include'\n    });\n\n    // Clear local storage\n    localStorage.removeItem('accessToken');\n    localStorage.removeItem('refreshToken');\n    return null;\n  } catch (error) {\n    return rejectWithValue(error.message);\n  }\n});\n\n// Auth slice\nconst authSlice = createSlice({\n  name: 'auth',\n  initialState,\n  reducers: {\n    // Synchronous actions\n    setUser: (state, action) => {\n      state.user = action.payload;\n      state.isAuthenticated = !!action.payload;\n    },\n    setProfile: (state, action) => {\n      state.profile = action.payload;\n    },\n    setTokens: (state, action) => {\n      state.accessToken = action.payload.accessToken;\n      state.tokenExpiry = Date.now() + action.payload.expiresIn * 1000;\n\n      // Store in localStorage\n      localStorage.setItem('accessToken', action.payload.accessToken);\n    },\n    clearAuth: state => {\n      state.user = null;\n      state.profile = null;\n      state.isAuthenticated = false;\n      state.accessToken = null;\n      state.refreshToken = null;\n      state.tokenExpiry = null;\n      state.error = null;\n\n      // Clear localStorage\n      localStorage.removeItem('accessToken');\n      localStorage.removeItem('refreshToken');\n    },\n    clearError: state => {\n      state.error = null;\n    }\n  },\n  extraReducers: builder => {\n    // Login user\n    builder.addCase(loginUser.pending, state => {\n      state.isLoading = true;\n      state.error = null;\n    }).addCase(loginUser.fulfilled, (state, action) => {\n      state.isLoading = false;\n      state.user = action.payload.user;\n      state.isAuthenticated = true;\n      state.accessToken = action.payload.accessToken;\n      state.refreshToken = action.payload.refreshToken;\n      state.tokenExpiry = Date.now() + action.payload.expiresIn * 1000;\n    }).addCase(loginUser.rejected, (state, action) => {\n      state.isLoading = false;\n      state.error = action.payload;\n    });\n\n    // Refresh token\n    builder.addCase(refreshAccessToken.fulfilled, (state, action) => {\n      state.accessToken = action.payload.accessToken;\n      state.tokenExpiry = Date.now() + action.payload.expiresIn * 1000;\n      localStorage.setItem('accessToken', action.payload.accessToken);\n    }).addCase(refreshAccessToken.rejected, state => {\n      // If refresh fails, clear auth state\n      state.user = null;\n      state.isAuthenticated = false;\n      state.accessToken = null;\n      state.refreshToken = null;\n      state.tokenExpiry = null;\n      localStorage.removeItem('accessToken');\n      localStorage.removeItem('refreshToken');\n    });\n\n    // Logout user\n    builder.addCase(logoutUser.fulfilled, state => {\n      state.user = null;\n      state.profile = null;\n      state.isAuthenticated = false;\n      state.accessToken = null;\n      state.refreshToken = null;\n      state.tokenExpiry = null;\n      state.error = null;\n    });\n    builder.addCase(authenticateUser.fulfilled, (state, action) => {\n      state.isLoading = false;\n      state.user = action.payload.user;\n      state.isAuthenticated = true;\n    }).addCase(authenticateUser.rejected, (state, action) => {\n      state.isLoading = false;\n      state.error = action.payload;\n    });\n  }\n});\nexport const {\n  setUser,\n  setProfile,\n  setTokens,\n  clearAuth,\n  clearError\n} = authSlice.actions;\nexport default authSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "initialState", "user", "profile", "isAuthenticated", "isLoading", "error", "accessToken", "localStorage", "getItem", "tokenExpiry", "loginUser", "credentials", "rejectWithValue", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "ok", "Error", "data", "json", "message", "authenticateUser", "refreshAccessToken", "_", "getState", "state", "refreshToken", "auth", "logoutUser", "removeItem", "authSlice", "name", "reducers", "setUser", "action", "payload", "setProfile", "setTokens", "Date", "now", "expiresIn", "setItem", "clearAuth", "clearError", "extraReducers", "builder", "addCase", "pending", "fulfilled", "rejected", "actions", "reducer"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/app/store/slices/authSlice.ts"], "sourcesContent": ["// Authentication Redux slice\r\nimport { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';\r\nimport { AuthState, AuthUser, UserProfile } from '../../../shared/types';\r\n\r\n// Initial state\r\nconst initialState: AuthState = {\r\n  user: null,\r\n  profile: null,\r\n  isAuthenticated: false,\r\n  isLoading: false,\r\n  error: null,\r\n  accessToken: localStorage.getItem('accessToken'),\r\n  tokenExpiry: null,\r\n};\r\n\r\n// Async thunks (will be implemented with actual API calls later)\r\nexport const loginUser = createAsyncThunk(\r\n  'auth/loginUser',\r\n  async (credentials: { email: string; password: string }, { rejectWithValue }) => {\r\n    try {\r\n      // TODO: Replace with actual API call\r\n      const response = await fetch('/api/auth/login', {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify(credentials),\r\n      });\r\n      \r\n      if (!response.ok) {\r\n        throw new Error('Login failed');\r\n      }\r\n      \r\n      const data = await response.json();\r\n      return data;\r\n    } catch (error: any) {\r\n      return rejectWithValue(error.message);\r\n    }\r\n  }\r\n);\r\n\r\nexport const authenticateUser = createAsyncThunk(\r\n  'auth/authenticate',\r\n  async (credentials: { token: string }, { rejectWithValue }) => {\r\n    try {\r\n\r\n      const response = await fetch('/api/auth/token', {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify(credentials),\r\n      });\r\n      \r\n      if (!response.ok) {\r\n        throw new Error('Authentication failed');\r\n      }\r\n      \r\n      const data = await response.json();\r\n      return data;\r\n    } catch (error: any) {\r\n      return rejectWithValue(error.message);\r\n    }\r\n  }\r\n);\r\n\r\nexport const refreshAccessToken = createAsyncThunk(\r\n  'auth/refreshAccessToken',\r\n  async (_, { getState, rejectWithValue }) => {\r\n    try {\r\n      const state = getState() as { auth: AuthState };\r\n      const refreshToken = state.auth.refreshToken;\r\n      \r\n      if (!refreshToken) {\r\n        throw new Error('No refresh token available');\r\n      }\r\n      \r\n      // TODO: Replace with actual API call\r\n      const response = await fetch('/api/auth/refresh', {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({ refreshToken }),\r\n      });\r\n      \r\n      if (!response.ok) {\r\n        throw new Error('Token refresh failed');\r\n      }\r\n      \r\n      const data = await response.json();\r\n      return data;\r\n    } catch (error: any) {\r\n      return rejectWithValue(error.message);\r\n    }\r\n  }\r\n);\r\n\r\nexport const logoutUser = createAsyncThunk(\r\n  'auth/logoutUser',\r\n  async (_, { rejectWithValue }) => {\r\n    try {\r\n      // TODO: Replace with actual API call\r\n      await fetch('/api/auth/logout', {\r\n        method: 'POST',\r\n        credentials: 'include',\r\n      });\r\n      \r\n      // Clear local storage\r\n      localStorage.removeItem('accessToken');\r\n      localStorage.removeItem('refreshToken');\r\n      \r\n      return null;\r\n    } catch (error: any) {\r\n      return rejectWithValue(error.message);\r\n    }\r\n  }\r\n);\r\n\r\n// Auth slice\r\nconst authSlice = createSlice({\r\n  name: 'auth',\r\n  initialState,\r\n  reducers: {\r\n    // Synchronous actions\r\n    setUser: (state, action: PayloadAction<AuthUser | null>) => {\r\n      state.user = action.payload;\r\n      state.isAuthenticated = !!action.payload;\r\n    },\r\n    \r\n    setProfile: (state, action: PayloadAction<UserProfile | null>) => {\r\n      state.profile = action.payload;\r\n    },\r\n    \r\n    setTokens: (state, action: PayloadAction<{ accessToken: string;  expiresIn: number }>) => {\r\n      state.accessToken = action.payload.accessToken;\r\n      state.tokenExpiry = Date.now() + (action.payload.expiresIn * 1000);\r\n      \r\n      // Store in localStorage\r\n      localStorage.setItem('accessToken', action.payload.accessToken);\r\n    },\r\n    \r\n    clearAuth: (state) => {\r\n      state.user = null;\r\n      state.profile = null;\r\n      state.isAuthenticated = false;\r\n      state.accessToken = null;\r\n      state.refreshToken = null;\r\n      state.tokenExpiry = null;\r\n      state.error = null;\r\n      \r\n      // Clear localStorage\r\n      localStorage.removeItem('accessToken');\r\n      localStorage.removeItem('refreshToken');\r\n    },\r\n    \r\n    clearError: (state) => {\r\n      state.error = null;\r\n    },\r\n  },\r\n  \r\n  extraReducers: (builder) => {\r\n    // Login user\r\n    builder\r\n      .addCase(loginUser.pending, (state) => {\r\n        state.isLoading = true;\r\n        state.error = null;\r\n      })\r\n      .addCase(loginUser.fulfilled, (state, action) => {\r\n        state.isLoading = false;\r\n        state.user = action.payload.user;\r\n        state.isAuthenticated = true;\r\n        state.accessToken = action.payload.accessToken;\r\n        state.refreshToken = action.payload.refreshToken;\r\n        state.tokenExpiry = Date.now() + (action.payload.expiresIn * 1000);\r\n      })\r\n      .addCase(loginUser.rejected, (state, action) => {\r\n        state.isLoading = false;\r\n        state.error = action.payload as string;\r\n      });\r\n    \r\n    // Refresh token\r\n    builder\r\n      .addCase(refreshAccessToken.fulfilled, (state, action) => {\r\n        state.accessToken = action.payload.accessToken;\r\n        state.tokenExpiry = Date.now() + (action.payload.expiresIn * 1000);\r\n        localStorage.setItem('accessToken', action.payload.accessToken);\r\n      })\r\n      .addCase(refreshAccessToken.rejected, (state) => {\r\n        // If refresh fails, clear auth state\r\n        state.user = null;\r\n        state.isAuthenticated = false;\r\n        state.accessToken = null;\r\n        state.refreshToken = null;\r\n        state.tokenExpiry = null;\r\n        localStorage.removeItem('accessToken');\r\n        localStorage.removeItem('refreshToken');\r\n      });\r\n    \r\n    // Logout user\r\n    builder\r\n      .addCase(logoutUser.fulfilled, (state) => {\r\n        state.user = null;\r\n        state.profile = null;\r\n        state.isAuthenticated = false;\r\n        state.accessToken = null;\r\n        state.refreshToken = null;\r\n        state.tokenExpiry = null;\r\n        state.error = null;\r\n      });\r\n\r\n    builder\r\n      .addCase(authenticateUser.fulfilled, (state, action) => {\r\n        state.isLoading = false;\r\n        state.user = action.payload.user;\r\n        state.isAuthenticated = true;\r\n\r\n      })\r\n      .addCase(authenticateUser.rejected, (state, action) => {\r\n        state.isLoading = false;\r\n        state.error = action.payload as string;\r\n      });\r\n  },\r\n});\r\n\r\nexport const { setUser, setProfile, setTokens, clearAuth, clearError } = authSlice.actions;\r\nexport default authSlice.reducer;\r\n"], "mappings": "AAAA;AACA,SAASA,WAAW,EAAEC,gBAAgB,QAAuB,kBAAkB;AAG/E;AACA,MAAMC,YAAuB,GAAG;EAC9BC,IAAI,EAAE,IAAI;EACVC,OAAO,EAAE,IAAI;EACbC,eAAe,EAAE,KAAK;EACtBC,SAAS,EAAE,KAAK;EAChBC,KAAK,EAAE,IAAI;EACXC,WAAW,EAAEC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;EAChDC,WAAW,EAAE;AACf,CAAC;;AAED;AACA,OAAO,MAAMC,SAAS,GAAGX,gBAAgB,CACvC,gBAAgB,EAChB,OAAOY,WAAgD,EAAE;EAAEC;AAAgB,CAAC,KAAK;EAC/E,IAAI;IACF;IACA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,iBAAiB,EAAE;MAC9CC,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QAAE,cAAc,EAAE;MAAmB,CAAC;MAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACR,WAAW;IAClC,CAAC,CAAC;IAEF,IAAI,CAACE,QAAQ,CAACO,EAAE,EAAE;MAChB,MAAM,IAAIC,KAAK,CAAC,cAAc,CAAC;IACjC;IAEA,MAAMC,IAAI,GAAG,MAAMT,QAAQ,CAACU,IAAI,CAAC,CAAC;IAClC,OAAOD,IAAI;EACb,CAAC,CAAC,OAAOjB,KAAU,EAAE;IACnB,OAAOO,eAAe,CAACP,KAAK,CAACmB,OAAO,CAAC;EACvC;AACF,CACF,CAAC;AAED,OAAO,MAAMC,gBAAgB,GAAG1B,gBAAgB,CAC9C,mBAAmB,EACnB,OAAOY,WAA8B,EAAE;EAAEC;AAAgB,CAAC,KAAK;EAC7D,IAAI;IAEF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,iBAAiB,EAAE;MAC9CC,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QAAE,cAAc,EAAE;MAAmB,CAAC;MAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACR,WAAW;IAClC,CAAC,CAAC;IAEF,IAAI,CAACE,QAAQ,CAACO,EAAE,EAAE;MAChB,MAAM,IAAIC,KAAK,CAAC,uBAAuB,CAAC;IAC1C;IAEA,MAAMC,IAAI,GAAG,MAAMT,QAAQ,CAACU,IAAI,CAAC,CAAC;IAClC,OAAOD,IAAI;EACb,CAAC,CAAC,OAAOjB,KAAU,EAAE;IACnB,OAAOO,eAAe,CAACP,KAAK,CAACmB,OAAO,CAAC;EACvC;AACF,CACF,CAAC;AAED,OAAO,MAAME,kBAAkB,GAAG3B,gBAAgB,CAChD,yBAAyB,EACzB,OAAO4B,CAAC,EAAE;EAAEC,QAAQ;EAAEhB;AAAgB,CAAC,KAAK;EAC1C,IAAI;IACF,MAAMiB,KAAK,GAAGD,QAAQ,CAAC,CAAwB;IAC/C,MAAME,YAAY,GAAGD,KAAK,CAACE,IAAI,CAACD,YAAY;IAE5C,IAAI,CAACA,YAAY,EAAE;MACjB,MAAM,IAAIT,KAAK,CAAC,4BAA4B,CAAC;IAC/C;;IAEA;IACA,MAAMR,QAAQ,GAAG,MAAMC,KAAK,CAAC,mBAAmB,EAAE;MAChDC,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QAAE,cAAc,EAAE;MAAmB,CAAC;MAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAEW;MAAa,CAAC;IACvC,CAAC,CAAC;IAEF,IAAI,CAACjB,QAAQ,CAACO,EAAE,EAAE;MAChB,MAAM,IAAIC,KAAK,CAAC,sBAAsB,CAAC;IACzC;IAEA,MAAMC,IAAI,GAAG,MAAMT,QAAQ,CAACU,IAAI,CAAC,CAAC;IAClC,OAAOD,IAAI;EACb,CAAC,CAAC,OAAOjB,KAAU,EAAE;IACnB,OAAOO,eAAe,CAACP,KAAK,CAACmB,OAAO,CAAC;EACvC;AACF,CACF,CAAC;AAED,OAAO,MAAMQ,UAAU,GAAGjC,gBAAgB,CACxC,iBAAiB,EACjB,OAAO4B,CAAC,EAAE;EAAEf;AAAgB,CAAC,KAAK;EAChC,IAAI;IACF;IACA,MAAME,KAAK,CAAC,kBAAkB,EAAE;MAC9BC,MAAM,EAAE,MAAM;MACdJ,WAAW,EAAE;IACf,CAAC,CAAC;;IAEF;IACAJ,YAAY,CAAC0B,UAAU,CAAC,aAAa,CAAC;IACtC1B,YAAY,CAAC0B,UAAU,CAAC,cAAc,CAAC;IAEvC,OAAO,IAAI;EACb,CAAC,CAAC,OAAO5B,KAAU,EAAE;IACnB,OAAOO,eAAe,CAACP,KAAK,CAACmB,OAAO,CAAC;EACvC;AACF,CACF,CAAC;;AAED;AACA,MAAMU,SAAS,GAAGpC,WAAW,CAAC;EAC5BqC,IAAI,EAAE,MAAM;EACZnC,YAAY;EACZoC,QAAQ,EAAE;IACR;IACAC,OAAO,EAAEA,CAACR,KAAK,EAAES,MAAsC,KAAK;MAC1DT,KAAK,CAAC5B,IAAI,GAAGqC,MAAM,CAACC,OAAO;MAC3BV,KAAK,CAAC1B,eAAe,GAAG,CAAC,CAACmC,MAAM,CAACC,OAAO;IAC1C,CAAC;IAEDC,UAAU,EAAEA,CAACX,KAAK,EAAES,MAAyC,KAAK;MAChET,KAAK,CAAC3B,OAAO,GAAGoC,MAAM,CAACC,OAAO;IAChC,CAAC;IAEDE,SAAS,EAAEA,CAACZ,KAAK,EAAES,MAAkE,KAAK;MACxFT,KAAK,CAACvB,WAAW,GAAGgC,MAAM,CAACC,OAAO,CAACjC,WAAW;MAC9CuB,KAAK,CAACpB,WAAW,GAAGiC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAIL,MAAM,CAACC,OAAO,CAACK,SAAS,GAAG,IAAK;;MAElE;MACArC,YAAY,CAACsC,OAAO,CAAC,aAAa,EAAEP,MAAM,CAACC,OAAO,CAACjC,WAAW,CAAC;IACjE,CAAC;IAEDwC,SAAS,EAAGjB,KAAK,IAAK;MACpBA,KAAK,CAAC5B,IAAI,GAAG,IAAI;MACjB4B,KAAK,CAAC3B,OAAO,GAAG,IAAI;MACpB2B,KAAK,CAAC1B,eAAe,GAAG,KAAK;MAC7B0B,KAAK,CAACvB,WAAW,GAAG,IAAI;MACxBuB,KAAK,CAACC,YAAY,GAAG,IAAI;MACzBD,KAAK,CAACpB,WAAW,GAAG,IAAI;MACxBoB,KAAK,CAACxB,KAAK,GAAG,IAAI;;MAElB;MACAE,YAAY,CAAC0B,UAAU,CAAC,aAAa,CAAC;MACtC1B,YAAY,CAAC0B,UAAU,CAAC,cAAc,CAAC;IACzC,CAAC;IAEDc,UAAU,EAAGlB,KAAK,IAAK;MACrBA,KAAK,CAACxB,KAAK,GAAG,IAAI;IACpB;EACF,CAAC;EAED2C,aAAa,EAAGC,OAAO,IAAK;IAC1B;IACAA,OAAO,CACJC,OAAO,CAACxC,SAAS,CAACyC,OAAO,EAAGtB,KAAK,IAAK;MACrCA,KAAK,CAACzB,SAAS,GAAG,IAAI;MACtByB,KAAK,CAACxB,KAAK,GAAG,IAAI;IACpB,CAAC,CAAC,CACD6C,OAAO,CAACxC,SAAS,CAAC0C,SAAS,EAAE,CAACvB,KAAK,EAAES,MAAM,KAAK;MAC/CT,KAAK,CAACzB,SAAS,GAAG,KAAK;MACvByB,KAAK,CAAC5B,IAAI,GAAGqC,MAAM,CAACC,OAAO,CAACtC,IAAI;MAChC4B,KAAK,CAAC1B,eAAe,GAAG,IAAI;MAC5B0B,KAAK,CAACvB,WAAW,GAAGgC,MAAM,CAACC,OAAO,CAACjC,WAAW;MAC9CuB,KAAK,CAACC,YAAY,GAAGQ,MAAM,CAACC,OAAO,CAACT,YAAY;MAChDD,KAAK,CAACpB,WAAW,GAAGiC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAIL,MAAM,CAACC,OAAO,CAACK,SAAS,GAAG,IAAK;IACpE,CAAC,CAAC,CACDM,OAAO,CAACxC,SAAS,CAAC2C,QAAQ,EAAE,CAACxB,KAAK,EAAES,MAAM,KAAK;MAC9CT,KAAK,CAACzB,SAAS,GAAG,KAAK;MACvByB,KAAK,CAACxB,KAAK,GAAGiC,MAAM,CAACC,OAAiB;IACxC,CAAC,CAAC;;IAEJ;IACAU,OAAO,CACJC,OAAO,CAACxB,kBAAkB,CAAC0B,SAAS,EAAE,CAACvB,KAAK,EAAES,MAAM,KAAK;MACxDT,KAAK,CAACvB,WAAW,GAAGgC,MAAM,CAACC,OAAO,CAACjC,WAAW;MAC9CuB,KAAK,CAACpB,WAAW,GAAGiC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAIL,MAAM,CAACC,OAAO,CAACK,SAAS,GAAG,IAAK;MAClErC,YAAY,CAACsC,OAAO,CAAC,aAAa,EAAEP,MAAM,CAACC,OAAO,CAACjC,WAAW,CAAC;IACjE,CAAC,CAAC,CACD4C,OAAO,CAACxB,kBAAkB,CAAC2B,QAAQ,EAAGxB,KAAK,IAAK;MAC/C;MACAA,KAAK,CAAC5B,IAAI,GAAG,IAAI;MACjB4B,KAAK,CAAC1B,eAAe,GAAG,KAAK;MAC7B0B,KAAK,CAACvB,WAAW,GAAG,IAAI;MACxBuB,KAAK,CAACC,YAAY,GAAG,IAAI;MACzBD,KAAK,CAACpB,WAAW,GAAG,IAAI;MACxBF,YAAY,CAAC0B,UAAU,CAAC,aAAa,CAAC;MACtC1B,YAAY,CAAC0B,UAAU,CAAC,cAAc,CAAC;IACzC,CAAC,CAAC;;IAEJ;IACAgB,OAAO,CACJC,OAAO,CAAClB,UAAU,CAACoB,SAAS,EAAGvB,KAAK,IAAK;MACxCA,KAAK,CAAC5B,IAAI,GAAG,IAAI;MACjB4B,KAAK,CAAC3B,OAAO,GAAG,IAAI;MACpB2B,KAAK,CAAC1B,eAAe,GAAG,KAAK;MAC7B0B,KAAK,CAACvB,WAAW,GAAG,IAAI;MACxBuB,KAAK,CAACC,YAAY,GAAG,IAAI;MACzBD,KAAK,CAACpB,WAAW,GAAG,IAAI;MACxBoB,KAAK,CAACxB,KAAK,GAAG,IAAI;IACpB,CAAC,CAAC;IAEJ4C,OAAO,CACJC,OAAO,CAACzB,gBAAgB,CAAC2B,SAAS,EAAE,CAACvB,KAAK,EAAES,MAAM,KAAK;MACtDT,KAAK,CAACzB,SAAS,GAAG,KAAK;MACvByB,KAAK,CAAC5B,IAAI,GAAGqC,MAAM,CAACC,OAAO,CAACtC,IAAI;MAChC4B,KAAK,CAAC1B,eAAe,GAAG,IAAI;IAE9B,CAAC,CAAC,CACD+C,OAAO,CAACzB,gBAAgB,CAAC4B,QAAQ,EAAE,CAACxB,KAAK,EAAES,MAAM,KAAK;MACrDT,KAAK,CAACzB,SAAS,GAAG,KAAK;MACvByB,KAAK,CAACxB,KAAK,GAAGiC,MAAM,CAACC,OAAiB;IACxC,CAAC,CAAC;EACN;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEF,OAAO;EAAEG,UAAU;EAAEC,SAAS;EAAEK,SAAS;EAAEC;AAAW,CAAC,GAAGb,SAAS,CAACoB,OAAO;AAC1F,eAAepB,SAAS,CAACqB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}