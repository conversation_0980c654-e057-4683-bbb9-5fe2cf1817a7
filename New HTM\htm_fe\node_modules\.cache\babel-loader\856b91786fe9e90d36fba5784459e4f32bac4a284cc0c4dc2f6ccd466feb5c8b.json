{"ast": null, "code": "// Firebase configuration (migrated from existing firebase-config.ts)\nimport { initializeApp } from 'firebase/app';\nimport { getDatabase } from 'firebase/database';\nimport { getAuth } from 'firebase/auth';\n\n// Firebase configuration - using actual config from existing firebase-config.ts\nconst firebaseConfig = {\n  apiKey: \"AIzaSyDQ85ZOo4kgfXrhI-aeJbr-08ykydG3ZE8\",\n  authDomain: \"htm-be.firebaseapp.com\",\n  projectId: \"htm-be\",\n  appId: \"1:508443789197:web:c900e6305300e355be9fc4\",\n  databaseURL: \"https://htm-be-default-rtdb.asia-southeast1.firebasedatabase.app/\"\n};\n\n// Initialize Firebase\nconst app = initializeApp(firebaseConfig);\n\n// Initialize Firebase services\nexport const database = getDatabase(app);\nexport const auth = getAuth(app);\nexport default app;", "map": {"version": 3, "names": ["initializeApp", "getDatabase", "getAuth", "firebaseConfig", "<PERSON><PERSON><PERSON><PERSON>", "authDomain", "projectId", "appId", "databaseURL", "app", "database", "auth"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/shared/services/firebase/config.ts"], "sourcesContent": ["// Firebase configuration (migrated from existing firebase-config.ts)\r\nimport { initializeApp } from 'firebase/app';\r\nimport { getDatabase, Database } from 'firebase/database';\r\nimport { getAuth, Auth } from 'firebase/auth';\r\n\r\n// Firebase configuration - using actual config from existing firebase-config.ts\r\nconst firebaseConfig = {\r\n  apiKey: \"AIzaSyDQ85ZOo4kgfXrhI-aeJbr-08ykydG3ZE8\",\r\n  authDomain: \"htm-be.firebaseapp.com\",\r\n  projectId: \"htm-be\",\r\n  appId: \"1:508443789197:web:c900e6305300e355be9fc4\",\r\n  databaseURL: \"https://htm-be-default-rtdb.asia-southeast1.firebasedatabase.app/\"\r\n};\r\n\r\n// Initialize Firebase\r\nconst app = initializeApp(firebaseConfig);\r\n\r\n// Initialize Firebase services\r\nexport const database: Database = getDatabase(app);\r\nexport const auth: Auth = getAuth(app);\r\n\r\nexport default app;\r\n"], "mappings": "AAAA;AACA,SAASA,aAAa,QAAQ,cAAc;AAC5C,SAASC,WAAW,QAAkB,mBAAmB;AACzD,SAASC,OAAO,QAAc,eAAe;;AAE7C;AACA,MAAMC,cAAc,GAAG;EACrBC,MAAM,EAAE,yCAAyC;EACjDC,UAAU,EAAE,wBAAwB;EACpCC,SAAS,EAAE,QAAQ;EACnBC,KAAK,EAAE,2CAA2C;EAClDC,WAAW,EAAE;AACf,CAAC;;AAED;AACA,MAAMC,GAAG,GAAGT,aAAa,CAACG,cAAc,CAAC;;AAEzC;AACA,OAAO,MAAMO,QAAkB,GAAGT,WAAW,CAACQ,GAAG,CAAC;AAClD,OAAO,MAAME,IAAU,GAAGT,OAAO,CAACO,GAAG,CAAC;AAEtC,eAAeA,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}