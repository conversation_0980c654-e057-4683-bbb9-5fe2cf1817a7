{"ast": null, "code": "var _s = $RefreshSig$();\n// Game API hook\nimport { useCallback } from 'react';\nimport { useAppDispatch, useAppSelector } from '../../../app/store';\nimport { fetchQuestions, submitAnswer, updateScores, setCurrentQuestion, setQuestions, setScores, setRound2Grid, setRound4Grid, clearError } from '../../../app/store/slices/gameSlice';\nimport { gameApi } from '../../services/game/gameApi';\nexport const useGameApi = () => {\n  _s();\n  const dispatch = useAppDispatch();\n  const {\n    loading,\n    currentQuestion,\n    questions,\n    scores,\n    currentRound,\n    round2Grid,\n    round4Grid\n  } = useAppSelector(state => state.game);\n\n  /**\r\n   * Get questions for a round\r\n   */\n  const getQuestions = useCallback(async params => {\n    try {\n      const result = await dispatch(fetchQuestions(params)).unwrap();\n      return result;\n    } catch (error) {\n      throw error;\n    }\n  }, [dispatch]);\n\n  /**\r\n   * Get prefetch question\r\n   */\n  const getPrefetchQuestion = useCallback(async params => {\n    try {\n      const question = await gameApi.getPrefetchQuestion(params);\n      return question;\n    } catch (error) {\n      throw error;\n    }\n  }, []);\n\n  /**\r\n   * Get packet names\r\n   */\n  const getPacketNames = useCallback(async testName => {\n    try {\n      const packets = await gameApi.getPacketNames(testName);\n      return packets;\n    } catch (error) {\n      throw error;\n    }\n  }, []);\n\n  /**\r\n   * Send grid to players\r\n   */\n  const sendGrid = useCallback(async params => {\n    try {\n      const success = await gameApi.sendGrid(params);\n\n      // Update local state based on current round\n      if (currentRound === 2) {\n        var _params$grid$;\n        dispatch(setRound2Grid({\n          cells: params.grid,\n          rows: params.grid.length,\n          cols: ((_params$grid$ = params.grid[0]) === null || _params$grid$ === void 0 ? void 0 : _params$grid$.length) || 0,\n          horizontalRows: [],\n          cnv: '',\n          selectedRows: [],\n          correctRows: [],\n          incorrectRows: []\n        }));\n      } else if (currentRound === 4) {\n        // Convert grid to Round 4 format\n        const round4Cells = params.grid.map((row, rowIndex) => row.map((cell, colIndex) => ({\n          id: `${rowIndex}-${colIndex}`,\n          question: {},\n          // Will be populated later\n          isSelected: false,\n          isAnswered: false,\n          difficulty: 'easy',\n          points: 20\n        })));\n        dispatch(setRound4Grid({\n          cells: round4Cells,\n          selectedDifficulties: [],\n          starPositions: []\n        }));\n      }\n      return success;\n    } catch (error) {\n      throw error;\n    }\n  }, [dispatch, currentRound]);\n\n  /**\r\n   * Start a new round\r\n   */\n  const startRound = useCallback(async params => {\n    try {\n      await gameApi.startRound(params);\n    } catch (error) {\n      throw error;\n    }\n  }, []);\n\n  /**\r\n   * Submit player answer\r\n   */\n  const submitPlayerAnswer = useCallback(async params => {\n    try {\n      const result = await dispatch(submitAnswer(params)).unwrap();\n      return result;\n    } catch (error) {\n      throw error;\n    }\n  }, [dispatch]);\n\n  /**\r\n   * Broadcast player answers\r\n   */\n  const broadcastAnswers = useCallback(async roomId => {\n    try {\n      const answers = await gameApi.broadcastAnswers(roomId);\n      return answers;\n    } catch (error) {\n      throw error;\n    }\n  }, []);\n\n  /**\r\n   * Update game scoring\r\n   */\n  const updateGameScoring = useCallback(async params => {\n    try {\n      const result = await dispatch(updateScores(params)).unwrap();\n      return result;\n    } catch (error) {\n      throw error;\n    }\n  }, [dispatch]);\n\n  /**\r\n   * Update current turn\r\n   */\n  const updateTurn = useCallback(async (roomId, turn) => {\n    try {\n      await gameApi.updateTurn(roomId, turn);\n    } catch (error) {\n      throw error;\n    }\n  }, []);\n\n  /**\r\n   * Show game rules\r\n   */\n  const showRules = useCallback(async (roomId, roundNumber) => {\n    try {\n      await gameApi.showRules(roomId, roundNumber);\n    } catch (error) {\n      throw error;\n    }\n  }, []);\n\n  /**\r\n   * Hide game rules\r\n   */\n  const hideRules = useCallback(async roomId => {\n    try {\n      await gameApi.hideRules(roomId);\n    } catch (error) {\n      throw error;\n    }\n  }, []);\n\n  /**\r\n   * Set selected packet name\r\n   */\n  const setSelectedPacketName = useCallback(async (roomId, packetName) => {\n    try {\n      await gameApi.setSelectedPacketName(roomId, packetName);\n    } catch (error) {\n      throw error;\n    }\n  }, []);\n\n  /**\r\n   * Set current question (local state)\r\n   */\n  const setCurrentQuestionLocal = useCallback(question => {\n    dispatch(setCurrentQuestion(question));\n  }, [dispatch]);\n\n  /**\r\n   * Set questions (local state)\r\n   */\n  const setQuestionsLocal = useCallback(questions => {\n    dispatch(setQuestions(questions));\n  }, [dispatch]);\n\n  /**\r\n   * Set scores (local state)\r\n   */\n  const setScoresLocal = useCallback(scores => {\n    dispatch(setScores(scores));\n  }, [dispatch]);\n\n  /**\r\n   * Clear game errors\r\n   */\n  const clearGameError = useCallback(() => {\n    dispatch(clearError());\n  }, [dispatch]);\n  return {\n    // State\n    loading,\n    currentQuestion,\n    questions,\n    scores,\n    currentRound,\n    round2Grid,\n    round4Grid,\n    // API Actions\n    getQuestions,\n    getPrefetchQuestion,\n    getPacketNames,\n    sendGrid,\n    startRound,\n    submitPlayerAnswer,\n    broadcastAnswers,\n    updateGameScoring,\n    updateTurn,\n    showRules,\n    hideRules,\n    setSelectedPacketName,\n    // Local State Actions\n    setCurrentQuestionLocal,\n    setQuestionsLocal,\n    setScoresLocal,\n    clearGameError\n  };\n};\n_s(useGameApi, \"pAoPz6dg8vobdlcfWDjSuv3Gc+0=\", false, function () {\n  return [useAppDispatch, useAppSelector];\n});\nexport default useGameApi;", "map": {"version": 3, "names": ["useCallback", "useAppDispatch", "useAppSelector", "fetchQuestions", "submitAnswer", "updateScores", "setCurrentQuestion", "setQuestions", "setScores", "setRound2Grid", "setRound4Grid", "clearError", "gameApi", "useGameApi", "_s", "dispatch", "loading", "currentQuestion", "questions", "scores", "currentRound", "round2Grid", "round4Grid", "state", "game", "getQuestions", "params", "result", "unwrap", "error", "getPrefetchQuestion", "question", "getPacketNames", "testName", "packets", "sendGrid", "success", "_params$grid$", "cells", "grid", "rows", "length", "cols", "horizontalRows", "cnv", "selectedRows", "correctRows", "incorrectRows", "round4Cells", "map", "row", "rowIndex", "cell", "colIndex", "id", "isSelected", "isAnswered", "difficulty", "points", "selectedDifficulties", "starPositions", "startRound", "submitPlayerAnswer", "broadcastAnswers", "roomId", "answers", "updateGameScoring", "updateTurn", "turn", "showRules", "roundNumber", "hideRules", "setSelectedPacketName", "packetName", "setCurrentQuestionLocal", "setQuestionsLocal", "setScoresLocal", "clearGameError"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/shared/hooks/api/useGameApi.ts"], "sourcesContent": ["// Game API hook\r\nimport { useCallback } from 'react';\r\nimport { useAppDispatch, useAppSelector } from '../../../app/store';\r\nimport { \r\n  fetchQuestions,\r\n  submitAnswer,\r\n  updateScores,\r\n  setCurrentQuestion,\r\n  setQuestions,\r\n  setScores,\r\n  setRound2Grid,\r\n  setRound4Grid,\r\n  clearError\r\n} from '../../../app/store/slices/gameSlice';\r\nimport { gameApi } from '../../services/game/gameApi';\r\nimport { \r\n  GetQuestionsRequest,\r\n  SubmitAnswerRequest,\r\n  ScoringRequest,\r\n  SendGridRequest,\r\n  Question,\r\n  Score\r\n} from '../../types';\r\n\r\nexport const useGameApi = () => {\r\n  const dispatch = useAppDispatch();\r\n  const { \r\n    loading, \r\n    currentQuestion, \r\n    questions, \r\n    scores, \r\n    currentRound,\r\n    round2Grid,\r\n    round4Grid \r\n  } = useAppSelector(state => state.game);\r\n\r\n  /**\r\n   * Get questions for a round\r\n   */\r\n  const getQuestions = useCallback(async (params: GetQuestionsRequest) => {\r\n    try {\r\n      const result = await dispatch(fetchQuestions(params)).unwrap();\r\n      return result;\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  }, [dispatch]);\r\n\r\n  /**\r\n   * Get prefetch question\r\n   */\r\n  const getPrefetchQuestion = useCallback(async (params: { \r\n    testName: string; \r\n    round: number; \r\n    questionNumber: number \r\n  }) => {\r\n    try {\r\n      const question = await gameApi.getPrefetchQuestion(params);\r\n      return question;\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  /**\r\n   * Get packet names\r\n   */\r\n  const getPacketNames = useCallback(async (testName: string) => {\r\n    try {\r\n      const packets = await gameApi.getPacketNames(testName);\r\n      return packets;\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  /**\r\n   * Send grid to players\r\n   */\r\n  const sendGrid = useCallback(async (params: SendGridRequest) => {\r\n    try {\r\n      const success = await gameApi.sendGrid(params);\r\n      \r\n      // Update local state based on current round\r\n      if (currentRound === 2) {\r\n        dispatch(setRound2Grid({\r\n          cells: params.grid,\r\n          rows: params.grid.length,\r\n          cols: params.grid[0]?.length || 0,\r\n          horizontalRows: [],\r\n          cnv: '',\r\n          selectedRows: [],\r\n          correctRows: [],\r\n          incorrectRows: [],\r\n        }));\r\n      } else if (currentRound === 4) {\r\n        // Convert grid to Round 4 format\r\n        const round4Cells = params.grid.map((row, rowIndex) =>\r\n          row.map((cell, colIndex) => ({\r\n            id: `${rowIndex}-${colIndex}`,\r\n            question: {} as Question, // Will be populated later\r\n            isSelected: false,\r\n            isAnswered: false,\r\n            difficulty: 'easy' as const,\r\n            points: 20,\r\n          }))\r\n        );\r\n        \r\n        dispatch(setRound4Grid({\r\n          cells: round4Cells,\r\n          selectedDifficulties: [],\r\n          starPositions: [],\r\n        }));\r\n      }\r\n      \r\n      return success;\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  }, [dispatch, currentRound]);\r\n\r\n  /**\r\n   * Start a new round\r\n   */\r\n  const startRound = useCallback(async (params: { \r\n    roomId: string; \r\n    round: string; \r\n    grid?: string[][] \r\n  }) => {\r\n    try {\r\n      await gameApi.startRound(params);\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  /**\r\n   * Submit player answer\r\n   */\r\n  const submitPlayerAnswer = useCallback(async (params: SubmitAnswerRequest) => {\r\n    try {\r\n      const result = await dispatch(submitAnswer(params)).unwrap();\r\n      return result;\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  }, [dispatch]);\r\n\r\n  /**\r\n   * Broadcast player answers\r\n   */\r\n  const broadcastAnswers = useCallback(async (roomId: string) => {\r\n    try {\r\n      const answers = await gameApi.broadcastAnswers(roomId);\r\n      return answers;\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  /**\r\n   * Update game scoring\r\n   */\r\n  const updateGameScoring = useCallback(async (params: ScoringRequest) => {\r\n    try {\r\n      const result = await dispatch(updateScores(params)).unwrap();\r\n      return result;\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  }, [dispatch]);\r\n\r\n  /**\r\n   * Update current turn\r\n   */\r\n  const updateTurn = useCallback(async (roomId: string, turn: number) => {\r\n    try {\r\n      await gameApi.updateTurn(roomId, turn);\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  /**\r\n   * Show game rules\r\n   */\r\n  const showRules = useCallback(async (roomId: string, roundNumber: string) => {\r\n    try {\r\n      await gameApi.showRules(roomId, roundNumber);\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  /**\r\n   * Hide game rules\r\n   */\r\n  const hideRules = useCallback(async (roomId: string) => {\r\n    try {\r\n      await gameApi.hideRules(roomId);\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  /**\r\n   * Set selected packet name\r\n   */\r\n  const setSelectedPacketName = useCallback(async (roomId: string, packetName: string) => {\r\n    try {\r\n      await gameApi.setSelectedPacketName(roomId, packetName);\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  /**\r\n   * Set current question (local state)\r\n   */\r\n  const setCurrentQuestionLocal = useCallback((question: Question | null) => {\r\n    dispatch(setCurrentQuestion(question));\r\n  }, [dispatch]);\r\n\r\n  /**\r\n   * Set questions (local state)\r\n   */\r\n  const setQuestionsLocal = useCallback((questions: Question[]) => {\r\n    dispatch(setQuestions(questions));\r\n  }, [dispatch]);\r\n\r\n  /**\r\n   * Set scores (local state)\r\n   */\r\n  const setScoresLocal = useCallback((scores: Score[]) => {\r\n    dispatch(setScores(scores));\r\n  }, [dispatch]);\r\n\r\n  /**\r\n   * Clear game errors\r\n   */\r\n  const clearGameError = useCallback(() => {\r\n    dispatch(clearError());\r\n  }, [dispatch]);\r\n\r\n  return {\r\n    // State\r\n    loading,\r\n    currentQuestion,\r\n    questions,\r\n    scores,\r\n    currentRound,\r\n    round2Grid,\r\n    round4Grid,\r\n    \r\n    // API Actions\r\n    getQuestions,\r\n    getPrefetchQuestion,\r\n    getPacketNames,\r\n    sendGrid,\r\n    startRound,\r\n    submitPlayerAnswer,\r\n    broadcastAnswers,\r\n    updateGameScoring,\r\n    updateTurn,\r\n    showRules,\r\n    hideRules,\r\n    setSelectedPacketName,\r\n    \r\n    // Local State Actions\r\n    setCurrentQuestionLocal,\r\n    setQuestionsLocal,\r\n    setScoresLocal,\r\n    clearGameError,\r\n  };\r\n};\r\n\r\nexport default useGameApi;\r\n"], "mappings": ";AAAA;AACA,SAASA,WAAW,QAAQ,OAAO;AACnC,SAASC,cAAc,EAAEC,cAAc,QAAQ,oBAAoB;AACnE,SACEC,cAAc,EACdC,YAAY,EACZC,YAAY,EACZC,kBAAkB,EAClBC,YAAY,EACZC,SAAS,EACTC,aAAa,EACbC,aAAa,EACbC,UAAU,QACL,qCAAqC;AAC5C,SAASC,OAAO,QAAQ,6BAA6B;AAUrD,OAAO,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAMC,QAAQ,GAAGd,cAAc,CAAC,CAAC;EACjC,MAAM;IACJe,OAAO;IACPC,eAAe;IACfC,SAAS;IACTC,MAAM;IACNC,YAAY;IACZC,UAAU;IACVC;EACF,CAAC,GAAGpB,cAAc,CAACqB,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC;;EAEvC;AACF;AACA;EACE,MAAMC,YAAY,GAAGzB,WAAW,CAAC,MAAO0B,MAA2B,IAAK;IACtE,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMZ,QAAQ,CAACZ,cAAc,CAACuB,MAAM,CAAC,CAAC,CAACE,MAAM,CAAC,CAAC;MAC9D,OAAOD,MAAM;IACf,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC,EAAE,CAACd,QAAQ,CAAC,CAAC;;EAEd;AACF;AACA;EACE,MAAMe,mBAAmB,GAAG9B,WAAW,CAAC,MAAO0B,MAI9C,IAAK;IACJ,IAAI;MACF,MAAMK,QAAQ,GAAG,MAAMnB,OAAO,CAACkB,mBAAmB,CAACJ,MAAM,CAAC;MAC1D,OAAOK,QAAQ;IACjB,CAAC,CAAC,OAAOF,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;AACF;AACA;EACE,MAAMG,cAAc,GAAGhC,WAAW,CAAC,MAAOiC,QAAgB,IAAK;IAC7D,IAAI;MACF,MAAMC,OAAO,GAAG,MAAMtB,OAAO,CAACoB,cAAc,CAACC,QAAQ,CAAC;MACtD,OAAOC,OAAO;IAChB,CAAC,CAAC,OAAOL,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;AACF;AACA;EACE,MAAMM,QAAQ,GAAGnC,WAAW,CAAC,MAAO0B,MAAuB,IAAK;IAC9D,IAAI;MACF,MAAMU,OAAO,GAAG,MAAMxB,OAAO,CAACuB,QAAQ,CAACT,MAAM,CAAC;;MAE9C;MACA,IAAIN,YAAY,KAAK,CAAC,EAAE;QAAA,IAAAiB,aAAA;QACtBtB,QAAQ,CAACN,aAAa,CAAC;UACrB6B,KAAK,EAAEZ,MAAM,CAACa,IAAI;UAClBC,IAAI,EAAEd,MAAM,CAACa,IAAI,CAACE,MAAM;UACxBC,IAAI,EAAE,EAAAL,aAAA,GAAAX,MAAM,CAACa,IAAI,CAAC,CAAC,CAAC,cAAAF,aAAA,uBAAdA,aAAA,CAAgBI,MAAM,KAAI,CAAC;UACjCE,cAAc,EAAE,EAAE;UAClBC,GAAG,EAAE,EAAE;UACPC,YAAY,EAAE,EAAE;UAChBC,WAAW,EAAE,EAAE;UACfC,aAAa,EAAE;QACjB,CAAC,CAAC,CAAC;MACL,CAAC,MAAM,IAAI3B,YAAY,KAAK,CAAC,EAAE;QAC7B;QACA,MAAM4B,WAAW,GAAGtB,MAAM,CAACa,IAAI,CAACU,GAAG,CAAC,CAACC,GAAG,EAAEC,QAAQ,KAChDD,GAAG,CAACD,GAAG,CAAC,CAACG,IAAI,EAAEC,QAAQ,MAAM;UAC3BC,EAAE,EAAE,GAAGH,QAAQ,IAAIE,QAAQ,EAAE;UAC7BtB,QAAQ,EAAE,CAAC,CAAa;UAAE;UAC1BwB,UAAU,EAAE,KAAK;UACjBC,UAAU,EAAE,KAAK;UACjBC,UAAU,EAAE,MAAe;UAC3BC,MAAM,EAAE;QACV,CAAC,CAAC,CACJ,CAAC;QAED3C,QAAQ,CAACL,aAAa,CAAC;UACrB4B,KAAK,EAAEU,WAAW;UAClBW,oBAAoB,EAAE,EAAE;UACxBC,aAAa,EAAE;QACjB,CAAC,CAAC,CAAC;MACL;MAEA,OAAOxB,OAAO;IAChB,CAAC,CAAC,OAAOP,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC,EAAE,CAACd,QAAQ,EAAEK,YAAY,CAAC,CAAC;;EAE5B;AACF;AACA;EACE,MAAMyC,UAAU,GAAG7D,WAAW,CAAC,MAAO0B,MAIrC,IAAK;IACJ,IAAI;MACF,MAAMd,OAAO,CAACiD,UAAU,CAACnC,MAAM,CAAC;IAClC,CAAC,CAAC,OAAOG,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;AACF;AACA;EACE,MAAMiC,kBAAkB,GAAG9D,WAAW,CAAC,MAAO0B,MAA2B,IAAK;IAC5E,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMZ,QAAQ,CAACX,YAAY,CAACsB,MAAM,CAAC,CAAC,CAACE,MAAM,CAAC,CAAC;MAC5D,OAAOD,MAAM;IACf,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC,EAAE,CAACd,QAAQ,CAAC,CAAC;;EAEd;AACF;AACA;EACE,MAAMgD,gBAAgB,GAAG/D,WAAW,CAAC,MAAOgE,MAAc,IAAK;IAC7D,IAAI;MACF,MAAMC,OAAO,GAAG,MAAMrD,OAAO,CAACmD,gBAAgB,CAACC,MAAM,CAAC;MACtD,OAAOC,OAAO;IAChB,CAAC,CAAC,OAAOpC,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;AACF;AACA;EACE,MAAMqC,iBAAiB,GAAGlE,WAAW,CAAC,MAAO0B,MAAsB,IAAK;IACtE,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMZ,QAAQ,CAACV,YAAY,CAACqB,MAAM,CAAC,CAAC,CAACE,MAAM,CAAC,CAAC;MAC5D,OAAOD,MAAM;IACf,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC,EAAE,CAACd,QAAQ,CAAC,CAAC;;EAEd;AACF;AACA;EACE,MAAMoD,UAAU,GAAGnE,WAAW,CAAC,OAAOgE,MAAc,EAAEI,IAAY,KAAK;IACrE,IAAI;MACF,MAAMxD,OAAO,CAACuD,UAAU,CAACH,MAAM,EAAEI,IAAI,CAAC;IACxC,CAAC,CAAC,OAAOvC,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;AACF;AACA;EACE,MAAMwC,SAAS,GAAGrE,WAAW,CAAC,OAAOgE,MAAc,EAAEM,WAAmB,KAAK;IAC3E,IAAI;MACF,MAAM1D,OAAO,CAACyD,SAAS,CAACL,MAAM,EAAEM,WAAW,CAAC;IAC9C,CAAC,CAAC,OAAOzC,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;AACF;AACA;EACE,MAAM0C,SAAS,GAAGvE,WAAW,CAAC,MAAOgE,MAAc,IAAK;IACtD,IAAI;MACF,MAAMpD,OAAO,CAAC2D,SAAS,CAACP,MAAM,CAAC;IACjC,CAAC,CAAC,OAAOnC,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;AACF;AACA;EACE,MAAM2C,qBAAqB,GAAGxE,WAAW,CAAC,OAAOgE,MAAc,EAAES,UAAkB,KAAK;IACtF,IAAI;MACF,MAAM7D,OAAO,CAAC4D,qBAAqB,CAACR,MAAM,EAAES,UAAU,CAAC;IACzD,CAAC,CAAC,OAAO5C,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;AACF;AACA;EACE,MAAM6C,uBAAuB,GAAG1E,WAAW,CAAE+B,QAAyB,IAAK;IACzEhB,QAAQ,CAACT,kBAAkB,CAACyB,QAAQ,CAAC,CAAC;EACxC,CAAC,EAAE,CAAChB,QAAQ,CAAC,CAAC;;EAEd;AACF;AACA;EACE,MAAM4D,iBAAiB,GAAG3E,WAAW,CAAEkB,SAAqB,IAAK;IAC/DH,QAAQ,CAACR,YAAY,CAACW,SAAS,CAAC,CAAC;EACnC,CAAC,EAAE,CAACH,QAAQ,CAAC,CAAC;;EAEd;AACF;AACA;EACE,MAAM6D,cAAc,GAAG5E,WAAW,CAAEmB,MAAe,IAAK;IACtDJ,QAAQ,CAACP,SAAS,CAACW,MAAM,CAAC,CAAC;EAC7B,CAAC,EAAE,CAACJ,QAAQ,CAAC,CAAC;;EAEd;AACF;AACA;EACE,MAAM8D,cAAc,GAAG7E,WAAW,CAAC,MAAM;IACvCe,QAAQ,CAACJ,UAAU,CAAC,CAAC,CAAC;EACxB,CAAC,EAAE,CAACI,QAAQ,CAAC,CAAC;EAEd,OAAO;IACL;IACAC,OAAO;IACPC,eAAe;IACfC,SAAS;IACTC,MAAM;IACNC,YAAY;IACZC,UAAU;IACVC,UAAU;IAEV;IACAG,YAAY;IACZK,mBAAmB;IACnBE,cAAc;IACdG,QAAQ;IACR0B,UAAU;IACVC,kBAAkB;IAClBC,gBAAgB;IAChBG,iBAAiB;IACjBC,UAAU;IACVE,SAAS;IACTE,SAAS;IACTC,qBAAqB;IAErB;IACAE,uBAAuB;IACvBC,iBAAiB;IACjBC,cAAc;IACdC;EACF,CAAC;AACH,CAAC;AAAC/D,EAAA,CA1PWD,UAAU;EAAA,QACJZ,cAAc,EAS3BC,cAAc;AAAA;AAkPpB,eAAeW,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}