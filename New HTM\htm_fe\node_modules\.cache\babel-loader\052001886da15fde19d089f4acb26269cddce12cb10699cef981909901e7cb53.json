{"ast": null, "code": "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 20h4\",\n  key: \"ni2waw\"\n}], [\"path\", {\n  d: \"M12 16v6\",\n  key: \"c8a4gj\"\n}], [\"path\", {\n  d: \"M17 2h4v4\",\n  key: \"vhe59\"\n}], [\"path\", {\n  d: \"m21 2-5.46 5.46\",\n  key: \"19kypf\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"11\",\n  r: \"5\",\n  key: \"16gxyc\"\n}]];\nconst VenusAndMars = createLucideIcon(\"venus-and-mars\", __iconNode);\nexport { __iconNode, VenusAndMars as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "cx", "cy", "r", "VenusAndMars", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\node_modules\\lucide-react\\src\\icons\\venus-and-mars.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M10 20h4', key: 'ni2waw' }],\n  ['path', { d: 'M12 16v6', key: 'c8a4gj' }],\n  ['path', { d: 'M17 2h4v4', key: 'vhe59' }],\n  ['path', { d: 'm21 2-5.46 5.46', key: '19kypf' }],\n  ['circle', { cx: '12', cy: '11', r: '5', key: '16gxyc' }],\n];\n\n/**\n * @component @name VenusAndMars\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMjBoNCIgLz4KICA8cGF0aCBkPSJNMTIgMTZ2NiIgLz4KICA8cGF0aCBkPSJNMTcgMmg0djQiIC8+CiAgPHBhdGggZD0ibTIxIDItNS40NiA1LjQ2IiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMTEiIHI9IjUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/venus-and-mars\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst VenusAndMars = createLucideIcon('venus-and-mars', __iconNode);\n\nexport default VenusAndMars;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAS,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,iBAAmB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAA,CAAU,EAC1D;AAaM,MAAAI,YAAA,GAAeC,gBAAiB,mBAAkBP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}