{"ast": null, "code": "var _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\n// Debounce hook for performance optimization\nimport { useState, useEffect } from 'react';\nexport const useDebounce = (value, delay) => {\n  _s();\n  const [debouncedValue, setDebouncedValue] = useState(value);\n  useEffect(() => {\n    const handler = setTimeout(() => {\n      setDebouncedValue(value);\n    }, delay);\n    return () => {\n      clearTimeout(handler);\n    };\n  }, [value, delay]);\n  return debouncedValue;\n};\n\n// Debounced callback hook\n_s(useDebounce, \"KDuPAtDOgxm8PU6legVJOb3oOmA=\");\nexport const useDebouncedCallback = (callback, delay) => {\n  _s2();\n  const [debounceTimer, setDebounceTimer] = useState(null);\n  const debouncedCallback = (...args) => {\n    if (debounceTimer) {\n      clearTimeout(debounceTimer);\n    }\n    const newTimer = setTimeout(() => {\n      callback(...args);\n    }, delay);\n    setDebounceTimer(newTimer);\n  };\n\n  // Cleanup on unmount\n  useEffect(() => {\n    return () => {\n      if (debounceTimer) {\n        clearTimeout(debounceTimer);\n      }\n    };\n  }, [debounceTimer]);\n  return debouncedCallback;\n};\n_s2(useDebouncedCallback, \"bNATEeYaAz3wpbjoAkwIMruXRt4=\");\nexport default useDebounce;", "map": {"version": 3, "names": ["useState", "useEffect", "useDebounce", "value", "delay", "_s", "debounced<PERSON><PERSON><PERSON>", "setDebouncedValue", "handler", "setTimeout", "clearTimeout", "useDebouncedCallback", "callback", "_s2", "deboun<PERSON><PERSON><PERSON>r", "setDebounceTimer", "deboun<PERSON><PERSON><PERSON><PERSON>", "args", "newTimer"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/shared/hooks/common/useDebounce.ts"], "sourcesContent": ["// Debounce hook for performance optimization\r\nimport { useState, useEffect } from 'react';\r\n\r\nexport const useDebounce = <T>(value: T, delay: number): T => {\r\n  const [debouncedValue, setDebouncedValue] = useState<T>(value);\r\n\r\n  useEffect(() => {\r\n    const handler = setTimeout(() => {\r\n      setDebouncedValue(value);\r\n    }, delay);\r\n\r\n    return () => {\r\n      clearTimeout(handler);\r\n    };\r\n  }, [value, delay]);\r\n\r\n  return debouncedValue;\r\n};\r\n\r\n// Debounced callback hook\r\nexport const useDebouncedCallback = <T extends (...args: any[]) => any>(\r\n  callback: T,\r\n  delay: number\r\n): T => {\r\n  const [debounceTimer, setDebounceTimer] = useState<NodeJS.Timeout | null>(null);\r\n\r\n  const debouncedCallback = ((...args: Parameters<T>) => {\r\n    if (debounceTimer) {\r\n      clearTimeout(debounceTimer);\r\n    }\r\n\r\n    const newTimer = setTimeout(() => {\r\n      callback(...args);\r\n    }, delay);\r\n\r\n    setDebounceTimer(newTimer);\r\n  }) as T;\r\n\r\n  // Cleanup on unmount\r\n  useEffect(() => {\r\n    return () => {\r\n      if (debounceTimer) {\r\n        clearTimeout(debounceTimer);\r\n      }\r\n    };\r\n  }, [debounceTimer]);\r\n\r\n  return debouncedCallback;\r\n};\r\n\r\nexport default useDebounce;\r\n"], "mappings": ";;AAAA;AACA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAE3C,OAAO,MAAMC,WAAW,GAAGA,CAAIC,KAAQ,EAAEC,KAAa,KAAQ;EAAAC,EAAA;EAC5D,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGP,QAAQ,CAAIG,KAAK,CAAC;EAE9DF,SAAS,CAAC,MAAM;IACd,MAAMO,OAAO,GAAGC,UAAU,CAAC,MAAM;MAC/BF,iBAAiB,CAACJ,KAAK,CAAC;IAC1B,CAAC,EAAEC,KAAK,CAAC;IAET,OAAO,MAAM;MACXM,YAAY,CAACF,OAAO,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,CAACL,KAAK,EAAEC,KAAK,CAAC,CAAC;EAElB,OAAOE,cAAc;AACvB,CAAC;;AAED;AAAAD,EAAA,CAhBaH,WAAW;AAiBxB,OAAO,MAAMS,oBAAoB,GAAGA,CAClCC,QAAW,EACXR,KAAa,KACP;EAAAS,GAAA;EACN,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGf,QAAQ,CAAwB,IAAI,CAAC;EAE/E,MAAMgB,iBAAiB,GAAIA,CAAC,GAAGC,IAAmB,KAAK;IACrD,IAAIH,aAAa,EAAE;MACjBJ,YAAY,CAACI,aAAa,CAAC;IAC7B;IAEA,MAAMI,QAAQ,GAAGT,UAAU,CAAC,MAAM;MAChCG,QAAQ,CAAC,GAAGK,IAAI,CAAC;IACnB,CAAC,EAAEb,KAAK,CAAC;IAETW,gBAAgB,CAACG,QAAQ,CAAC;EAC5B,CAAO;;EAEP;EACAjB,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX,IAAIa,aAAa,EAAE;QACjBJ,YAAY,CAACI,aAAa,CAAC;MAC7B;IACF,CAAC;EACH,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;EAEnB,OAAOE,iBAAiB;AAC1B,CAAC;AAACH,GAAA,CA5BWF,oBAAoB;AA8BjC,eAAeT,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}