{"ast": null, "code": "// API-related constants\nexport const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://127.0.0.1:8000/api';\nexport const API_ENDPOINTS = {\n  // Authentication\n  AUTH: {\n    LOGIN: '/auth/login',\n    LOGOUT: '/auth/logout',\n    REFRESH: '/auth/refresh',\n    VERIFY: '/auth/verify',\n    ACCESS_TOKEN: '/auth/access_token'\n  },\n  // Game\n  GAME: {\n    QUESTIONS: '/game/question/round',\n    PREFETCH: '/game/question/prefetch',\n    PACKETS: '/game/question/round/packet',\n    GRID: '/game/grid',\n    ROUND_START: '/game/round/start',\n    SCORING: '/game/scoring',\n    ANSWER: '/game/answer',\n    BROADCAST_ANSWER: '/game/broadcast/answer',\n    TURN: '/game/turn',\n    RULES: '/game/rules'\n  },\n  // Room\n  ROOM: {\n    BASE: '/room',\n    JOIN: '/room/join',\n    VALIDATE: '/room/validate',\n    LEAVE: '/room/leave',\n    SPECTATOR: '/room/spectator'\n  },\n  // Test Management\n  TEST: {\n    BASE: '/test',\n    UPLOAD: '/test/upload',\n    USER: '/test/user',\n    UPDATE: '/test/update'\n  },\n  // File Upload\n  S3: {\n    PRESIGNED_URL: '/s3/presigned-url',\n    FILES: '/s3/files',\n    SAVE_KEY: '/s3/save-file-key'\n  },\n  // Sound\n  SOUND: {\n    PLAY: '/sound/play'\n  },\n  // Buzz\n  BUZZ: {\n    BASE: '/buzz',\n    OPEN: '/buzz/open',\n    CLOSE: '/buzz/close',\n    RESET: '/buzz/reset'\n  },\n  // Star\n  STAR: {\n    BASE: '/star'\n  },\n  // History\n  HISTORY: {\n    UPDATE: '/history/update',\n    RETRIEVE: '/history/retrive' // Note: keeping original typo for compatibility\n  }\n};\nexport const HTTP_STATUS = {\n  OK: 200,\n  CREATED: 201,\n  BAD_REQUEST: 400,\n  UNAUTHORIZED: 401,\n  FORBIDDEN: 403,\n  NOT_FOUND: 404,\n  INTERNAL_SERVER_ERROR: 500\n};\nexport const REQUEST_TIMEOUT = 30000; // 30 seconds\n\nexport const RETRY_ATTEMPTS = 3;\nexport const RETRY_DELAY = 1000; // 1 second", "map": {"version": 3, "names": ["API_BASE_URL", "process", "env", "REACT_APP_API_BASE_URL", "API_ENDPOINTS", "AUTH", "LOGIN", "LOGOUT", "REFRESH", "VERIFY", "ACCESS_TOKEN", "GAME", "QUESTIONS", "PREFETCH", "PACKETS", "GRID", "ROUND_START", "SCORING", "ANSWER", "BROADCAST_ANSWER", "TURN", "RULES", "ROOM", "BASE", "JOIN", "VALIDATE", "LEAVE", "SPECTATOR", "TEST", "UPLOAD", "USER", "UPDATE", "S3", "PRESIGNED_URL", "FILES", "SAVE_KEY", "SOUND", "PLAY", "BUZZ", "OPEN", "CLOSE", "RESET", "STAR", "HISTORY", "RETRIEVE", "HTTP_STATUS", "OK", "CREATED", "BAD_REQUEST", "UNAUTHORIZED", "FORBIDDEN", "NOT_FOUND", "INTERNAL_SERVER_ERROR", "REQUEST_TIMEOUT", "RETRY_ATTEMPTS", "RETRY_DELAY"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/shared/constants/api.constants.ts"], "sourcesContent": ["// API-related constants\r\nexport const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://127.0.0.1:8000/api';\r\n\r\nexport const API_ENDPOINTS = {\r\n  // Authentication\r\n  AUTH: {\r\n    LOGIN: '/auth/login',\r\n    LOGOUT: '/auth/logout',\r\n    REFRESH: '/auth/refresh',\r\n    VERIFY: '/auth/verify',\r\n    ACCESS_TOKEN: '/auth/access_token',\r\n  },\r\n  \r\n  // Game\r\n  GAME: {\r\n    QUESTIONS: '/game/question/round',\r\n    PREFETCH: '/game/question/prefetch',\r\n    PACKETS: '/game/question/round/packet',\r\n    GRID: '/game/grid',\r\n    ROUND_START: '/game/round/start',\r\n    SCORING: '/game/scoring',\r\n    ANSWER: '/game/answer',\r\n    BROADCAST_ANSWER: '/game/broadcast/answer',\r\n    TURN: '/game/turn',\r\n    RULES: '/game/rules',\r\n  },\r\n  \r\n  // Room\r\n  ROOM: {\r\n    BASE: '/room',\r\n    JOIN: '/room/join',\r\n    VALIDATE: '/room/validate',\r\n    LEAVE: '/room/leave',\r\n    SPECTATOR: '/room/spectator',\r\n  },\r\n  \r\n  // Test Management\r\n  TEST: {\r\n    BASE: '/test',\r\n    UPLOAD: '/test/upload',\r\n    USER: '/test/user',\r\n    UPDATE: '/test/update',\r\n  },\r\n  \r\n  // File Upload\r\n  S3: {\r\n    PRESIGNED_URL: '/s3/presigned-url',\r\n    FILES: '/s3/files',\r\n    SAVE_KEY: '/s3/save-file-key',\r\n  },\r\n  \r\n  // Sound\r\n  SOUND: {\r\n    PLAY: '/sound/play',\r\n  },\r\n  \r\n  // Buzz\r\n  BUZZ: {\r\n    BASE: '/buzz',\r\n    OPEN: '/buzz/open',\r\n    CLOSE: '/buzz/close',\r\n    RESET: '/buzz/reset',\r\n  },\r\n  \r\n  // Star\r\n  STAR: {\r\n    BASE: '/star',\r\n  },\r\n  \r\n  // History\r\n  HISTORY: {\r\n    UPDATE: '/history/update',\r\n    RETRIEVE: '/history/retrive', // Note: keeping original typo for compatibility\r\n  },\r\n} as const;\r\n\r\nexport const HTTP_STATUS = {\r\n  OK: 200,\r\n  CREATED: 201,\r\n  BAD_REQUEST: 400,\r\n  UNAUTHORIZED: 401,\r\n  FORBIDDEN: 403,\r\n  NOT_FOUND: 404,\r\n  INTERNAL_SERVER_ERROR: 500,\r\n} as const;\r\n\r\nexport const REQUEST_TIMEOUT = 30000; // 30 seconds\r\n\r\nexport const RETRY_ATTEMPTS = 3;\r\n\r\nexport const RETRY_DELAY = 1000; // 1 second\r\n"], "mappings": "AAAA;AACA,OAAO,MAAMA,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,2BAA2B;AAE7F,OAAO,MAAMC,aAAa,GAAG;EAC3B;EACAC,IAAI,EAAE;IACJC,KAAK,EAAE,aAAa;IACpBC,MAAM,EAAE,cAAc;IACtBC,OAAO,EAAE,eAAe;IACxBC,MAAM,EAAE,cAAc;IACtBC,YAAY,EAAE;EAChB,CAAC;EAED;EACAC,IAAI,EAAE;IACJC,SAAS,EAAE,sBAAsB;IACjCC,QAAQ,EAAE,yBAAyB;IACnCC,OAAO,EAAE,6BAA6B;IACtCC,IAAI,EAAE,YAAY;IAClBC,WAAW,EAAE,mBAAmB;IAChCC,OAAO,EAAE,eAAe;IACxBC,MAAM,EAAE,cAAc;IACtBC,gBAAgB,EAAE,wBAAwB;IAC1CC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE;EACT,CAAC;EAED;EACAC,IAAI,EAAE;IACJC,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,YAAY;IAClBC,QAAQ,EAAE,gBAAgB;IAC1BC,KAAK,EAAE,aAAa;IACpBC,SAAS,EAAE;EACb,CAAC;EAED;EACAC,IAAI,EAAE;IACJL,IAAI,EAAE,OAAO;IACbM,MAAM,EAAE,cAAc;IACtBC,IAAI,EAAE,YAAY;IAClBC,MAAM,EAAE;EACV,CAAC;EAED;EACAC,EAAE,EAAE;IACFC,aAAa,EAAE,mBAAmB;IAClCC,KAAK,EAAE,WAAW;IAClBC,QAAQ,EAAE;EACZ,CAAC;EAED;EACAC,KAAK,EAAE;IACLC,IAAI,EAAE;EACR,CAAC;EAED;EACAC,IAAI,EAAE;IACJf,IAAI,EAAE,OAAO;IACbgB,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,aAAa;IACpBC,KAAK,EAAE;EACT,CAAC;EAED;EACAC,IAAI,EAAE;IACJnB,IAAI,EAAE;EACR,CAAC;EAED;EACAoB,OAAO,EAAE;IACPZ,MAAM,EAAE,iBAAiB;IACzBa,QAAQ,EAAE,kBAAkB,CAAE;EAChC;AACF,CAAU;AAEV,OAAO,MAAMC,WAAW,GAAG;EACzBC,EAAE,EAAE,GAAG;EACPC,OAAO,EAAE,GAAG;EACZC,WAAW,EAAE,GAAG;EAChBC,YAAY,EAAE,GAAG;EACjBC,SAAS,EAAE,GAAG;EACdC,SAAS,EAAE,GAAG;EACdC,qBAAqB,EAAE;AACzB,CAAU;AAEV,OAAO,MAAMC,eAAe,GAAG,KAAK,CAAC,CAAC;;AAEtC,OAAO,MAAMC,cAAc,GAAG,CAAC;AAE/B,OAAO,MAAMC,WAAW,GAAG,IAAI,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}