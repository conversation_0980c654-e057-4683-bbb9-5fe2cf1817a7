{"ast": null, "code": "// Button variant styles using Tailwind CSS\n\nexport const buttonVariants = {\n  primary: 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500 text-white border-transparent',\n  secondary: 'bg-gray-200 hover:bg-gray-300 focus:ring-gray-500 text-gray-900 border-transparent',\n  danger: 'bg-red-600 hover:bg-red-700 focus:ring-red-500 text-white border-transparent',\n  success: 'bg-green-600 hover:bg-green-700 focus:ring-green-500 text-white border-transparent',\n  warning: 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500 text-white border-transparent',\n  ghost: 'bg-transparent hover:bg-gray-100 focus:ring-gray-500 text-gray-700 border-transparent',\n  outline: 'bg-transparent hover:bg-gray-50 focus:ring-blue-500 text-blue-600 border-blue-600'\n};\nexport const buttonSizes = {\n  xs: 'px-2.5 py-1.5 text-xs',\n  sm: 'px-3 py-2 text-sm',\n  md: 'px-4 py-2 text-base',\n  lg: 'px-6 py-3 text-lg',\n  xl: 'px-8 py-4 text-xl'\n};\nexport const baseButtonClasses = 'inline-flex items-center justify-center font-medium rounded-md border focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200';\nexport const loadingSpinnerClasses = 'animate-spin -ml-1 mr-2 h-4 w-4';\nexport const getButtonClasses = (variant, size, fullWidth, isDisabled, className) => {\n  const classes = [baseButtonClasses, buttonVariants[variant], buttonSizes[size], fullWidth ? 'w-full' : '', isDisabled ? 'opacity-50 cursor-not-allowed' : '', className || ''];\n  return classes.filter(Boolean).join(' ');\n};", "map": {"version": 3, "names": ["buttonVariants", "primary", "secondary", "danger", "success", "warning", "ghost", "outline", "buttonSizes", "xs", "sm", "md", "lg", "xl", "baseButtonClasses", "loadingSpinnerClasses", "getButtonClasses", "variant", "size", "fullWidth", "isDisabled", "className", "classes", "filter", "Boolean", "join"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/shared/components/ui/Button/Button.variants.ts"], "sourcesContent": ["// Button variant styles using Tailwind CSS\r\nimport { ButtonVariant, ButtonSize } from './Button.types';\r\n\r\nexport const buttonVariants: Record<ButtonVariant, string> = {\r\n  primary: 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500 text-white border-transparent',\r\n  secondary: 'bg-gray-200 hover:bg-gray-300 focus:ring-gray-500 text-gray-900 border-transparent',\r\n  danger: 'bg-red-600 hover:bg-red-700 focus:ring-red-500 text-white border-transparent',\r\n  success: 'bg-green-600 hover:bg-green-700 focus:ring-green-500 text-white border-transparent',\r\n  warning: 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500 text-white border-transparent',\r\n  ghost: 'bg-transparent hover:bg-gray-100 focus:ring-gray-500 text-gray-700 border-transparent',\r\n  outline: 'bg-transparent hover:bg-gray-50 focus:ring-blue-500 text-blue-600 border-blue-600',\r\n};\r\n\r\nexport const buttonSizes: Record<ButtonSize, string> = {\r\n  xs: 'px-2.5 py-1.5 text-xs',\r\n  sm: 'px-3 py-2 text-sm',\r\n  md: 'px-4 py-2 text-base',\r\n  lg: 'px-6 py-3 text-lg',\r\n  xl: 'px-8 py-4 text-xl',\r\n};\r\n\r\nexport const baseButtonClasses = 'inline-flex items-center justify-center font-medium rounded-md border focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200';\r\n\r\nexport const loadingSpinnerClasses = 'animate-spin -ml-1 mr-2 h-4 w-4';\r\n\r\nexport const getButtonClasses = (\r\n  variant: ButtonVariant,\r\n  size: ButtonSize,\r\n  fullWidth: boolean,\r\n  isDisabled: boolean,\r\n  className?: string\r\n): string => {\r\n  const classes = [\r\n    baseButtonClasses,\r\n    buttonVariants[variant],\r\n    buttonSizes[size],\r\n    fullWidth ? 'w-full' : '',\r\n    isDisabled ? 'opacity-50 cursor-not-allowed' : '',\r\n    className || '',\r\n  ];\r\n\r\n  return classes.filter(Boolean).join(' ');\r\n};\r\n"], "mappings": "AAAA;;AAGA,OAAO,MAAMA,cAA6C,GAAG;EAC3DC,OAAO,EAAE,iFAAiF;EAC1FC,SAAS,EAAE,oFAAoF;EAC/FC,MAAM,EAAE,8EAA8E;EACtFC,OAAO,EAAE,oFAAoF;EAC7FC,OAAO,EAAE,uFAAuF;EAChGC,KAAK,EAAE,uFAAuF;EAC9FC,OAAO,EAAE;AACX,CAAC;AAED,OAAO,MAAMC,WAAuC,GAAG;EACrDC,EAAE,EAAE,uBAAuB;EAC3BC,EAAE,EAAE,mBAAmB;EACvBC,EAAE,EAAE,qBAAqB;EACzBC,EAAE,EAAE,mBAAmB;EACvBC,EAAE,EAAE;AACN,CAAC;AAED,OAAO,MAAMC,iBAAiB,GAAG,0MAA0M;AAE3O,OAAO,MAAMC,qBAAqB,GAAG,iCAAiC;AAEtE,OAAO,MAAMC,gBAAgB,GAAGA,CAC9BC,OAAsB,EACtBC,IAAgB,EAChBC,SAAkB,EAClBC,UAAmB,EACnBC,SAAkB,KACP;EACX,MAAMC,OAAO,GAAG,CACdR,iBAAiB,EACjBd,cAAc,CAACiB,OAAO,CAAC,EACvBT,WAAW,CAACU,IAAI,CAAC,EACjBC,SAAS,GAAG,QAAQ,GAAG,EAAE,EACzBC,UAAU,GAAG,+BAA+B,GAAG,EAAE,EACjDC,SAAS,IAAI,EAAE,CAChB;EAED,OAAOC,OAAO,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;AAC1C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}