{"ast": null, "code": "// Authentication API service\nimport { api } from '../api/client';\nimport { API_ENDPOINTS } from '../../constants';\nexport const authApi = {\n  /**\r\n   * <PERSON>gin user with email and password\r\n   */\n  async login(credentials) {\n    const response = await api.post(API_ENDPOINTS.AUTH.LOGIN, credentials);\n    return response.data;\n  },\n  /**\r\n   * Generate access token (for room-based authentication)\r\n   */\n  async generateAccessToken(data) {\n    const response = await api.post(API_ENDPOINTS.AUTH.ACCESS_TOKEN, data);\n    return response.data.data;\n  },\n  /**\r\n   * Verify access token\r\n   */\n  async verifyToken() {\n    const response = await api.post(API_ENDPOINTS.AUTH.VERIFY);\n    return response.data.data;\n  },\n  /**\r\n   * Refresh access token\r\n   */\n  async refreshToken(refreshTokenData) {\n    const response = await api.post(API_ENDPOINTS.AUTH.REFRESH, refreshTokenData);\n    return response.data;\n  },\n  /**\r\n   * Logout user\r\n   */\n  async logout() {\n    await api.post(API_ENDPOINTS.AUTH.LOGOUT);\n\n    // Clear local storage\n    localStorage.removeItem('accessToken');\n    localStorage.removeItem('refreshToken');\n  },\n  /**\r\n   * Check if user is host (legacy compatibility)\r\n   */\n  async isHost(data) {\n    try {\n      // This would be replaced with actual API call\n      // For now, maintaining compatibility with existing code\n      const response = await api.post('/auth/is-host', data);\n      return response.data.data.isHost;\n    } catch (error) {\n      return false;\n    }\n  }\n};\nexport default authApi;", "map": {"version": 3, "names": ["api", "API_ENDPOINTS", "authApi", "login", "credentials", "response", "post", "AUTH", "LOGIN", "data", "generateAccessToken", "ACCESS_TOKEN", "verifyToken", "VERIFY", "refreshToken", "refreshTokenData", "REFRESH", "logout", "LOGOUT", "localStorage", "removeItem", "isHost", "error"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/shared/services/auth/authApi.ts"], "sourcesContent": ["// Authentication API service\r\nimport { api } from '../api/client';\r\nimport { API_ENDPOINTS } from '../../constants';\r\nimport { \r\n  LoginRequest, \r\n  LoginResponse, \r\n  RefreshTokenRequest, \r\n  RefreshTokenResponse \r\n} from '../../types';\r\n\r\nexport const authApi = {\r\n  /**\r\n   * <PERSON>gin user with email and password\r\n   */\r\n  async login(credentials: LoginRequest): Promise<LoginResponse> {\r\n    const response = await api.post<LoginResponse['data']>(\r\n      API_ENDPOINTS.AUTH.LOGIN,\r\n      credentials\r\n    );\r\n    return response.data;\r\n  },\r\n\r\n  /**\r\n   * Generate access token (for room-based authentication)\r\n   */\r\n  async generateAccessToken(data: { roomId: string; role: string }): Promise<{ accessToken: string; refreshToken: string }> {\r\n    const response = await api.post<{ accessToken: string; refreshToken: string }>(\r\n      API_ENDPOINTS.AUTH.ACCESS_TOKEN,\r\n      data\r\n    );\r\n    return response.data.data;\r\n  },\r\n\r\n  /**\r\n   * Verify access token\r\n   */\r\n  async verifyToken(): Promise<{ roomId: string; role: string; userId: string; exp: number }> {\r\n    const response = await api.post<{ roomId: string; role: string; userId: string; exp: number }>(\r\n      API_ENDPOINTS.AUTH.VERIFY\r\n    );\r\n    return response.data.data;\r\n  },\r\n\r\n  /**\r\n   * Refresh access token\r\n   */\r\n  async refreshToken(refreshTokenData: RefreshTokenRequest): Promise<RefreshTokenResponse> {\r\n    const response = await api.post<RefreshTokenResponse['data']>(\r\n      API_ENDPOINTS.AUTH.REFRESH,\r\n      refreshTokenData\r\n    );\r\n    return response.data;\r\n  },\r\n\r\n  /**\r\n   * Logout user\r\n   */\r\n  async logout(): Promise<void> {\r\n    await api.post(API_ENDPOINTS.AUTH.LOGOUT);\r\n    \r\n    // Clear local storage\r\n    localStorage.removeItem('accessToken');\r\n    localStorage.removeItem('refreshToken');\r\n  },\r\n\r\n  /**\r\n   * Check if user is host (legacy compatibility)\r\n   */\r\n  async isHost(data: any): Promise<boolean> {\r\n    try {\r\n      // This would be replaced with actual API call\r\n      // For now, maintaining compatibility with existing code\r\n      const response = await api.post<{ isHost: boolean }>('/auth/is-host', data);\r\n      return response.data.data.isHost;\r\n    } catch (error) {\r\n      return false;\r\n    }\r\n  },\r\n};\r\n\r\nexport default authApi;\r\n"], "mappings": "AAAA;AACA,SAASA,GAAG,QAAQ,eAAe;AACnC,SAASC,aAAa,QAAQ,iBAAiB;AAQ/C,OAAO,MAAMC,OAAO,GAAG;EACrB;AACF;AACA;EACE,MAAMC,KAAKA,CAACC,WAAyB,EAA0B;IAC7D,MAAMC,QAAQ,GAAG,MAAML,GAAG,CAACM,IAAI,CAC7BL,aAAa,CAACM,IAAI,CAACC,KAAK,EACxBJ,WACF,CAAC;IACD,OAAOC,QAAQ,CAACI,IAAI;EACtB,CAAC;EAED;AACF;AACA;EACE,MAAMC,mBAAmBA,CAACD,IAAsC,EAA0D;IACxH,MAAMJ,QAAQ,GAAG,MAAML,GAAG,CAACM,IAAI,CAC7BL,aAAa,CAACM,IAAI,CAACI,YAAY,EAC/BF,IACF,CAAC;IACD,OAAOJ,QAAQ,CAACI,IAAI,CAACA,IAAI;EAC3B,CAAC;EAED;AACF;AACA;EACE,MAAMG,WAAWA,CAAA,EAA2E;IAC1F,MAAMP,QAAQ,GAAG,MAAML,GAAG,CAACM,IAAI,CAC7BL,aAAa,CAACM,IAAI,CAACM,MACrB,CAAC;IACD,OAAOR,QAAQ,CAACI,IAAI,CAACA,IAAI;EAC3B,CAAC;EAED;AACF;AACA;EACE,MAAMK,YAAYA,CAACC,gBAAqC,EAAiC;IACvF,MAAMV,QAAQ,GAAG,MAAML,GAAG,CAACM,IAAI,CAC7BL,aAAa,CAACM,IAAI,CAACS,OAAO,EAC1BD,gBACF,CAAC;IACD,OAAOV,QAAQ,CAACI,IAAI;EACtB,CAAC;EAED;AACF;AACA;EACE,MAAMQ,MAAMA,CAAA,EAAkB;IAC5B,MAAMjB,GAAG,CAACM,IAAI,CAACL,aAAa,CAACM,IAAI,CAACW,MAAM,CAAC;;IAEzC;IACAC,YAAY,CAACC,UAAU,CAAC,aAAa,CAAC;IACtCD,YAAY,CAACC,UAAU,CAAC,cAAc,CAAC;EACzC,CAAC;EAED;AACF;AACA;EACE,MAAMC,MAAMA,CAACZ,IAAS,EAAoB;IACxC,IAAI;MACF;MACA;MACA,MAAMJ,QAAQ,GAAG,MAAML,GAAG,CAACM,IAAI,CAAsB,eAAe,EAAEG,IAAI,CAAC;MAC3E,OAAOJ,QAAQ,CAACI,IAAI,CAACA,IAAI,CAACY,MAAM;IAClC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd,OAAO,KAAK;IACd;EACF;AACF,CAAC;AAED,eAAepB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}