{"ast": null, "code": "// Token management service\nimport { TOKEN_REFRESH_INTERVAL } from '../../constants';\nexport class TokenService {\n  constructor(onTokenRefresh, onTokenExpired) {\n    this.refreshTimer = null;\n    this.onTokenRefresh = void 0;\n    this.onTokenExpired = void 0;\n    this.onTokenRefresh = onTokenRefresh;\n    this.onTokenExpired = onTokenExpired;\n  }\n\n  /**\r\n   * Get access token from localStorage\r\n   */\n  getAccessToken() {\n    return localStorage.getItem('accessToken');\n  }\n\n  /**\r\n   * Get refresh token from localStorage\r\n   */\n  getRefreshToken() {\n    return localStorage.getItem('refreshToken');\n  }\n\n  /**\r\n   * Set tokens in localStorage\r\n   */\n  setTokens(accessToken, refreshToken, expiresIn) {\n    localStorage.setItem('accessToken', accessToken);\n    localStorage.setItem('refreshToken', refreshToken);\n\n    // Calculate expiry time\n    const expiryTime = Date.now() + expiresIn * 1000;\n    localStorage.setItem('tokenExpiry', expiryTime.toString());\n\n    // Start auto-refresh timer\n    this.startAutoRefresh(expiresIn);\n  }\n\n  /**\r\n   * Clear all tokens\r\n   */\n  clearTokens() {\n    localStorage.removeItem('accessToken');\n    localStorage.removeItem('refreshToken');\n    localStorage.removeItem('tokenExpiry');\n    this.stopAutoRefresh();\n  }\n\n  /**\r\n   * Check if token is expired\r\n   */\n  isTokenExpired() {\n    const expiryTime = localStorage.getItem('tokenExpiry');\n    if (!expiryTime) return true;\n    return Date.now() > parseInt(expiryTime);\n  }\n\n  /**\r\n   * Get time until token expires (in milliseconds)\r\n   */\n  getTimeUntilExpiry() {\n    const expiryTime = localStorage.getItem('tokenExpiry');\n    if (!expiryTime) return 0;\n    return Math.max(0, parseInt(expiryTime) - Date.now());\n  }\n\n  /**\r\n   * Start automatic token refresh\r\n   */\n  startAutoRefresh(expiresIn) {\n    this.stopAutoRefresh();\n\n    // Refresh token 5 minutes before it expires, or at TOKEN_REFRESH_INTERVAL\n    const refreshTime = Math.min((expiresIn - 300) * 1000,\n    // 5 minutes before expiry\n    TOKEN_REFRESH_INTERVAL);\n    if (refreshTime > 0) {\n      this.refreshTimer = setTimeout(async () => {\n        await this.refreshAccessToken();\n      }, refreshTime);\n    }\n  }\n\n  /**\r\n   * Stop automatic token refresh\r\n   */\n  stopAutoRefresh() {\n    if (this.refreshTimer) {\n      clearTimeout(this.refreshTimer);\n      this.refreshTimer = null;\n    }\n  }\n\n  /**\r\n   * Refresh access token\r\n   */\n  async refreshAccessToken() {\n    try {\n      var _this$onTokenRefresh;\n      const refreshToken = this.getRefreshToken();\n      if (!refreshToken) {\n        var _this$onTokenExpired;\n        (_this$onTokenExpired = this.onTokenExpired) === null || _this$onTokenExpired === void 0 ? void 0 : _this$onTokenExpired.call(this);\n        return;\n      }\n\n      // Import here to avoid circular dependency\n      const {\n        authApi\n      } = await import('./authApi');\n      const response = await authApi.refreshToken({\n        refreshToken\n      });\n      const {\n        accessToken,\n        expiresIn\n      } = response.data;\n\n      // Update tokens\n      localStorage.setItem('accessToken', accessToken);\n      const expiryTime = Date.now() + expiresIn * 1000;\n      localStorage.setItem('tokenExpiry', expiryTime.toString());\n\n      // Notify callback\n      (_this$onTokenRefresh = this.onTokenRefresh) === null || _this$onTokenRefresh === void 0 ? void 0 : _this$onTokenRefresh.call(this, accessToken);\n\n      // Schedule next refresh\n      this.startAutoRefresh(expiresIn);\n    } catch (error) {\n      var _this$onTokenExpired2;\n      console.error('Token refresh failed:', error);\n      this.clearTokens();\n      (_this$onTokenExpired2 = this.onTokenExpired) === null || _this$onTokenExpired2 === void 0 ? void 0 : _this$onTokenExpired2.call(this);\n    }\n  }\n\n  /**\r\n   * Initialize token service (call on app startup)\r\n   */\n  initialize() {\n    const accessToken = this.getAccessToken();\n    const expiryTime = localStorage.getItem('tokenExpiry');\n    if (accessToken && expiryTime) {\n      const timeUntilExpiry = parseInt(expiryTime) - Date.now();\n      if (timeUntilExpiry > 0) {\n        // Token is still valid, start auto-refresh\n        this.startAutoRefresh(timeUntilExpiry / 1000);\n      } else {\n        // Token is expired, try to refresh\n        this.refreshAccessToken();\n      }\n    }\n  }\n\n  /**\r\n   * Cleanup (call on app unmount)\r\n   */\n  cleanup() {\n    this.stopAutoRefresh();\n  }\n}\n\n// Create singleton instance\nexport const tokenService = new TokenService();\nexport default tokenService;", "map": {"version": 3, "names": ["TOKEN_REFRESH_INTERVAL", "TokenService", "constructor", "onTokenRefresh", "onTokenExpired", "refreshTimer", "getAccessToken", "localStorage", "getItem", "getRefreshToken", "setTokens", "accessToken", "refreshToken", "expiresIn", "setItem", "expiryTime", "Date", "now", "toString", "startAutoRefresh", "clearTokens", "removeItem", "stopAutoRefresh", "isTokenExpired", "parseInt", "getTimeUntilExpiry", "Math", "max", "refreshTime", "min", "setTimeout", "refreshAccessToken", "clearTimeout", "_this$onTokenRefresh", "_this$onTokenExpired", "call", "authApi", "response", "data", "error", "_this$onTokenExpired2", "console", "initialize", "timeUntilExpiry", "cleanup", "tokenService"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/shared/services/auth/tokenService.ts"], "sourcesContent": ["// Token management service\r\nimport { TOKEN_REFRESH_INTERVAL } from '../../constants';\r\n\r\nexport class TokenService {\r\n  private refreshTimer: NodeJS.Timeout | null = null;\r\n  private onTokenRefresh?: (token: string) => void;\r\n  private onTokenExpired?: () => void;\r\n\r\n  constructor(\r\n    onTokenRefresh?: (token: string) => void,\r\n    onTokenExpired?: () => void\r\n  ) {\r\n    this.onTokenRefresh = onTokenRefresh;\r\n    this.onTokenExpired = onTokenExpired;\r\n  }\r\n\r\n  /**\r\n   * Get access token from localStorage\r\n   */\r\n  getAccessToken(): string | null {\r\n    return localStorage.getItem('accessToken');\r\n  }\r\n\r\n  /**\r\n   * Get refresh token from localStorage\r\n   */\r\n  getRefreshToken(): string | null {\r\n    return localStorage.getItem('refreshToken');\r\n  }\r\n\r\n  /**\r\n   * Set tokens in localStorage\r\n   */\r\n  setTokens(accessToken: string, refreshToken: string, expiresIn: number): void {\r\n    localStorage.setItem('accessToken', accessToken);\r\n    localStorage.setItem('refreshToken', refreshToken);\r\n    \r\n    // Calculate expiry time\r\n    const expiryTime = Date.now() + (expiresIn * 1000);\r\n    localStorage.setItem('tokenExpiry', expiryTime.toString());\r\n\r\n    // Start auto-refresh timer\r\n    this.startAutoRefresh(expiresIn);\r\n  }\r\n\r\n  /**\r\n   * Clear all tokens\r\n   */\r\n  clearTokens(): void {\r\n    localStorage.removeItem('accessToken');\r\n    localStorage.removeItem('refreshToken');\r\n    localStorage.removeItem('tokenExpiry');\r\n    this.stopAutoRefresh();\r\n  }\r\n\r\n  /**\r\n   * Check if token is expired\r\n   */\r\n  isTokenExpired(): boolean {\r\n    const expiryTime = localStorage.getItem('tokenExpiry');\r\n    if (!expiryTime) return true;\r\n\r\n    return Date.now() > parseInt(expiryTime);\r\n  }\r\n\r\n  /**\r\n   * Get time until token expires (in milliseconds)\r\n   */\r\n  getTimeUntilExpiry(): number {\r\n    const expiryTime = localStorage.getItem('tokenExpiry');\r\n    if (!expiryTime) return 0;\r\n\r\n    return Math.max(0, parseInt(expiryTime) - Date.now());\r\n  }\r\n\r\n  /**\r\n   * Start automatic token refresh\r\n   */\r\n  private startAutoRefresh(expiresIn: number): void {\r\n    this.stopAutoRefresh();\r\n\r\n    // Refresh token 5 minutes before it expires, or at TOKEN_REFRESH_INTERVAL\r\n    const refreshTime = Math.min(\r\n      (expiresIn - 300) * 1000, // 5 minutes before expiry\r\n      TOKEN_REFRESH_INTERVAL\r\n    );\r\n\r\n    if (refreshTime > 0) {\r\n      this.refreshTimer = setTimeout(async () => {\r\n        await this.refreshAccessToken();\r\n      }, refreshTime);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Stop automatic token refresh\r\n   */\r\n  private stopAutoRefresh(): void {\r\n    if (this.refreshTimer) {\r\n      clearTimeout(this.refreshTimer);\r\n      this.refreshTimer = null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Refresh access token\r\n   */\r\n  private async refreshAccessToken(): Promise<void> {\r\n    try {\r\n      const refreshToken = this.getRefreshToken();\r\n      if (!refreshToken) {\r\n        this.onTokenExpired?.();\r\n        return;\r\n      }\r\n\r\n      // Import here to avoid circular dependency\r\n      const { authApi } = await import('./authApi');\r\n      \r\n      const response = await authApi.refreshToken({ refreshToken });\r\n      const { accessToken, expiresIn } = response.data;\r\n\r\n      // Update tokens\r\n      localStorage.setItem('accessToken', accessToken);\r\n      const expiryTime = Date.now() + (expiresIn * 1000);\r\n      localStorage.setItem('tokenExpiry', expiryTime.toString());\r\n\r\n      // Notify callback\r\n      this.onTokenRefresh?.(accessToken);\r\n\r\n      // Schedule next refresh\r\n      this.startAutoRefresh(expiresIn);\r\n\r\n    } catch (error) {\r\n      console.error('Token refresh failed:', error);\r\n      this.clearTokens();\r\n      this.onTokenExpired?.();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Initialize token service (call on app startup)\r\n   */\r\n  initialize(): void {\r\n    const accessToken = this.getAccessToken();\r\n    const expiryTime = localStorage.getItem('tokenExpiry');\r\n\r\n    if (accessToken && expiryTime) {\r\n      const timeUntilExpiry = parseInt(expiryTime) - Date.now();\r\n      \r\n      if (timeUntilExpiry > 0) {\r\n        // Token is still valid, start auto-refresh\r\n        this.startAutoRefresh(timeUntilExpiry / 1000);\r\n      } else {\r\n        // Token is expired, try to refresh\r\n        this.refreshAccessToken();\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Cleanup (call on app unmount)\r\n   */\r\n  cleanup(): void {\r\n    this.stopAutoRefresh();\r\n  }\r\n}\r\n\r\n// Create singleton instance\r\nexport const tokenService = new TokenService();\r\n\r\nexport default tokenService;\r\n"], "mappings": "AAAA;AACA,SAASA,sBAAsB,QAAQ,iBAAiB;AAExD,OAAO,MAAMC,YAAY,CAAC;EAKxBC,WAAWA,CACTC,cAAwC,EACxCC,cAA2B,EAC3B;IAAA,KAPMC,YAAY,GAA0B,IAAI;IAAA,KAC1CF,cAAc;IAAA,KACdC,cAAc;IAMpB,IAAI,CAACD,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,cAAc,GAAGA,cAAc;EACtC;;EAEA;AACF;AACA;EACEE,cAAcA,CAAA,EAAkB;IAC9B,OAAOC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;EAC5C;;EAEA;AACF;AACA;EACEC,eAAeA,CAAA,EAAkB;IAC/B,OAAOF,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;EAC7C;;EAEA;AACF;AACA;EACEE,SAASA,CAACC,WAAmB,EAAEC,YAAoB,EAAEC,SAAiB,EAAQ;IAC5EN,YAAY,CAACO,OAAO,CAAC,aAAa,EAAEH,WAAW,CAAC;IAChDJ,YAAY,CAACO,OAAO,CAAC,cAAc,EAAEF,YAAY,CAAC;;IAElD;IACA,MAAMG,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAIJ,SAAS,GAAG,IAAK;IAClDN,YAAY,CAACO,OAAO,CAAC,aAAa,EAAEC,UAAU,CAACG,QAAQ,CAAC,CAAC,CAAC;;IAE1D;IACA,IAAI,CAACC,gBAAgB,CAACN,SAAS,CAAC;EAClC;;EAEA;AACF;AACA;EACEO,WAAWA,CAAA,EAAS;IAClBb,YAAY,CAACc,UAAU,CAAC,aAAa,CAAC;IACtCd,YAAY,CAACc,UAAU,CAAC,cAAc,CAAC;IACvCd,YAAY,CAACc,UAAU,CAAC,aAAa,CAAC;IACtC,IAAI,CAACC,eAAe,CAAC,CAAC;EACxB;;EAEA;AACF;AACA;EACEC,cAAcA,CAAA,EAAY;IACxB,MAAMR,UAAU,GAAGR,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACtD,IAAI,CAACO,UAAU,EAAE,OAAO,IAAI;IAE5B,OAAOC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGO,QAAQ,CAACT,UAAU,CAAC;EAC1C;;EAEA;AACF;AACA;EACEU,kBAAkBA,CAAA,EAAW;IAC3B,MAAMV,UAAU,GAAGR,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACtD,IAAI,CAACO,UAAU,EAAE,OAAO,CAAC;IAEzB,OAAOW,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEH,QAAQ,CAACT,UAAU,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;EACvD;;EAEA;AACF;AACA;EACUE,gBAAgBA,CAACN,SAAiB,EAAQ;IAChD,IAAI,CAACS,eAAe,CAAC,CAAC;;IAEtB;IACA,MAAMM,WAAW,GAAGF,IAAI,CAACG,GAAG,CAC1B,CAAChB,SAAS,GAAG,GAAG,IAAI,IAAI;IAAE;IAC1Bb,sBACF,CAAC;IAED,IAAI4B,WAAW,GAAG,CAAC,EAAE;MACnB,IAAI,CAACvB,YAAY,GAAGyB,UAAU,CAAC,YAAY;QACzC,MAAM,IAAI,CAACC,kBAAkB,CAAC,CAAC;MACjC,CAAC,EAAEH,WAAW,CAAC;IACjB;EACF;;EAEA;AACF;AACA;EACUN,eAAeA,CAAA,EAAS;IAC9B,IAAI,IAAI,CAACjB,YAAY,EAAE;MACrB2B,YAAY,CAAC,IAAI,CAAC3B,YAAY,CAAC;MAC/B,IAAI,CAACA,YAAY,GAAG,IAAI;IAC1B;EACF;;EAEA;AACF;AACA;EACE,MAAc0B,kBAAkBA,CAAA,EAAkB;IAChD,IAAI;MAAA,IAAAE,oBAAA;MACF,MAAMrB,YAAY,GAAG,IAAI,CAACH,eAAe,CAAC,CAAC;MAC3C,IAAI,CAACG,YAAY,EAAE;QAAA,IAAAsB,oBAAA;QACjB,CAAAA,oBAAA,OAAI,CAAC9B,cAAc,cAAA8B,oBAAA,uBAAnBA,oBAAA,CAAAC,IAAA,KAAsB,CAAC;QACvB;MACF;;MAEA;MACA,MAAM;QAAEC;MAAQ,CAAC,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC;MAE7C,MAAMC,QAAQ,GAAG,MAAMD,OAAO,CAACxB,YAAY,CAAC;QAAEA;MAAa,CAAC,CAAC;MAC7D,MAAM;QAAED,WAAW;QAAEE;MAAU,CAAC,GAAGwB,QAAQ,CAACC,IAAI;;MAEhD;MACA/B,YAAY,CAACO,OAAO,CAAC,aAAa,EAAEH,WAAW,CAAC;MAChD,MAAMI,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAIJ,SAAS,GAAG,IAAK;MAClDN,YAAY,CAACO,OAAO,CAAC,aAAa,EAAEC,UAAU,CAACG,QAAQ,CAAC,CAAC,CAAC;;MAE1D;MACA,CAAAe,oBAAA,OAAI,CAAC9B,cAAc,cAAA8B,oBAAA,uBAAnBA,oBAAA,CAAAE,IAAA,KAAI,EAAkBxB,WAAW,CAAC;;MAElC;MACA,IAAI,CAACQ,gBAAgB,CAACN,SAAS,CAAC;IAElC,CAAC,CAAC,OAAO0B,KAAK,EAAE;MAAA,IAAAC,qBAAA;MACdC,OAAO,CAACF,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,IAAI,CAACnB,WAAW,CAAC,CAAC;MAClB,CAAAoB,qBAAA,OAAI,CAACpC,cAAc,cAAAoC,qBAAA,uBAAnBA,qBAAA,CAAAL,IAAA,KAAsB,CAAC;IACzB;EACF;;EAEA;AACF;AACA;EACEO,UAAUA,CAAA,EAAS;IACjB,MAAM/B,WAAW,GAAG,IAAI,CAACL,cAAc,CAAC,CAAC;IACzC,MAAMS,UAAU,GAAGR,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IAEtD,IAAIG,WAAW,IAAII,UAAU,EAAE;MAC7B,MAAM4B,eAAe,GAAGnB,QAAQ,CAACT,UAAU,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;MAEzD,IAAI0B,eAAe,GAAG,CAAC,EAAE;QACvB;QACA,IAAI,CAACxB,gBAAgB,CAACwB,eAAe,GAAG,IAAI,CAAC;MAC/C,CAAC,MAAM;QACL;QACA,IAAI,CAACZ,kBAAkB,CAAC,CAAC;MAC3B;IACF;EACF;;EAEA;AACF;AACA;EACEa,OAAOA,CAAA,EAAS;IACd,IAAI,CAACtB,eAAe,CAAC,CAAC;EACxB;AACF;;AAEA;AACA,OAAO,MAAMuB,YAAY,GAAG,IAAI5C,YAAY,CAAC,CAAC;AAE9C,eAAe4C,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}