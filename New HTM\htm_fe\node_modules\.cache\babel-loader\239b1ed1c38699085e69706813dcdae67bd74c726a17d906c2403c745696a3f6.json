{"ast": null, "code": "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m16 16 3-8 3 8c-.87.65-1.92 1-3 1s-2.13-.35-3-1Z\",\n  key: \"7g6ntu\"\n}], [\"path\", {\n  d: \"m2 16 3-8 3 8c-.87.65-1.92 1-3 1s-2.13-.35-3-1Z\",\n  key: \"ijws7r\"\n}], [\"path\", {\n  d: \"M7 21h10\",\n  key: \"1b0cd5\"\n}], [\"path\", {\n  d: \"M12 3v18\",\n  key: \"108xh3\"\n}], [\"path\", {\n  d: \"M3 7h2c2 0 5-1 7-2 2 1 5 2 7 2h2\",\n  key: \"3gwbw2\"\n}]];\nconst Scale = createLucideIcon(\"scale\", __iconNode);\nexport { __iconNode, Scale as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Scale", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\node_modules\\lucide-react\\src\\icons\\scale.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm16 16 3-8 3 8c-.87.65-1.92 1-3 1s-2.13-.35-3-1Z', key: '7g6ntu' }],\n  ['path', { d: 'm2 16 3-8 3 8c-.87.65-1.92 1-3 1s-2.13-.35-3-1Z', key: 'ijws7r' }],\n  ['path', { d: 'M7 21h10', key: '1b0cd5' }],\n  ['path', { d: 'M12 3v18', key: '108xh3' }],\n  ['path', { d: 'M3 7h2c2 0 5-1 7-2 2 1 5 2 7 2h2', key: '3gwbw2' }],\n];\n\n/**\n * @component @name Scale\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTYgMTYgMy04IDMgOGMtLjg3LjY1LTEuOTIgMS0zIDFzLTIuMTMtLjM1LTMtMVoiIC8+CiAgPHBhdGggZD0ibTIgMTYgMy04IDMgOGMtLjg3LjY1LTEuOTIgMS0zIDFzLTIuMTMtLjM1LTMtMVoiIC8+CiAgPHBhdGggZD0iTTcgMjFoMTAiIC8+CiAgPHBhdGggZD0iTTEyIDN2MTgiIC8+CiAgPHBhdGggZD0iTTMgN2gyYzIgMCA1LTEgNy0yIDIgMSA1IDIgNyAyaDIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/scale\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Scale = createLucideIcon('scale', __iconNode);\n\nexport default Scale;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,kDAAoD;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjF,CAAC,MAAQ;EAAED,CAAA,EAAG,iDAAmD;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChF,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,kCAAoC;EAAAC,GAAA,EAAK;AAAA,CAAU,EACnE;AAaM,MAAAC,KAAA,GAAQC,gBAAiB,UAASJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}