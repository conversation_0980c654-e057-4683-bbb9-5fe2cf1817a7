{"ast": null, "code": "var _s = $RefreshSig$();\n// Authentication API hook\nimport { useCallback } from 'react';\nimport { useAppDispatch, useAppSelector } from '../../../app/store';\nimport { loginUser, refreshAccessToken, logoutUser, setTokens, clearAuth } from '../../../app/store/slices/authSlice';\nimport { authApi } from '../../services/auth/authApi';\nimport { tokenService } from '../../services/auth/tokenService';\nexport const useAuthApi = () => {\n  _s();\n  const dispatch = useAppDispatch();\n  const {\n    isLoading,\n    error,\n    isAuthenticated,\n    user\n  } = useAppSelector(state => state.auth);\n\n  /**\r\n   * Login user\r\n   */\n  const login = useCallback(async credentials => {\n    try {\n      const result = await dispatch(loginUser(credentials)).unwrap();\n\n      // Set tokens in token service\n      tokenService.setTokens(result.accessToken, result.refreshToken, result.expiresIn);\n      return result;\n    } catch (error) {\n      throw error;\n    }\n  }, [dispatch]);\n\n  /**\r\n   * Generate access token for room-based auth\r\n   */\n  const generateAccessToken = useCallback(async data => {\n    try {\n      const result = await authApi.generateAccessToken(data);\n\n      // Set tokens in Redux and token service\n      dispatch(setTokens({\n        accessToken: result.accessToken,\n        refreshToken: result.refreshToken,\n        expiresIn: 30 * 60 // 30 minutes default\n      }));\n      return result;\n    } catch (error) {\n      throw error;\n    }\n  }, [dispatch]);\n\n  /**\r\n   * Verify current token\r\n   */\n  const verifyToken = useCallback(async () => {\n    try {\n      const result = await authApi.verifyToken();\n      return result;\n    } catch (error) {\n      // Token is invalid, clear auth state\n      dispatch(clearAuth());\n      throw error;\n    }\n  }, [dispatch]);\n\n  /**\r\n   * Refresh access token\r\n   */\n  const refreshToken = useCallback(async () => {\n    try {\n      const result = await dispatch(refreshAccessToken()).unwrap();\n      return result;\n    } catch (error) {\n      // Refresh failed, clear auth state\n      dispatch(clearAuth());\n      throw error;\n    }\n  }, [dispatch]);\n\n  /**\r\n   * Logout user\r\n   */\n  const logout = useCallback(async () => {\n    try {\n      await dispatch(logoutUser()).unwrap();\n      tokenService.clearTokens();\n    } catch (error) {\n      // Even if logout fails, clear local state\n      dispatch(clearAuth());\n      tokenService.clearTokens();\n      throw error;\n    }\n  }, [dispatch]);\n\n  /**\r\n   * Check if user is host (legacy compatibility)\r\n   */\n  const isHost = useCallback(async data => {\n    try {\n      const result = await authApi.isHost(data);\n      return result;\n    } catch (error) {\n      console.error('Failed to check host status:', error);\n      return false;\n    }\n  }, []);\n\n  /**\r\n   * Initialize authentication (call on app startup)\r\n   */\n  const initializeAuth = useCallback(() => {\n    // Initialize token service\n    tokenService.initialize();\n\n    // Check if we have valid tokens\n    const accessToken = tokenService.getAccessToken();\n    if (accessToken && !tokenService.isTokenExpired()) {\n      // Verify token with backend\n      verifyToken().catch(() => {\n        // Token verification failed, clear auth\n        dispatch(clearAuth());\n      });\n    } else {\n      // No valid token, clear auth state\n      dispatch(clearAuth());\n    }\n  }, [dispatch, verifyToken]);\n  return {\n    // State\n    isLoading,\n    error,\n    isAuthenticated,\n    user,\n    // Actions\n    login,\n    generateAccessToken,\n    verifyToken,\n    refreshToken,\n    logout,\n    isHost,\n    initializeAuth\n  };\n};\n_s(useAuthApi, \"C4UVjweTGU1e8hG+p0aE7cwG/5I=\", false, function () {\n  return [useAppDispatch, useAppSelector];\n});\nexport default useAuthApi;", "map": {"version": 3, "names": ["useCallback", "useAppDispatch", "useAppSelector", "loginUser", "refreshAccessToken", "logoutUser", "setTokens", "clearAuth", "authApi", "tokenService", "useAuthApi", "_s", "dispatch", "isLoading", "error", "isAuthenticated", "user", "state", "auth", "login", "credentials", "result", "unwrap", "accessToken", "refreshToken", "expiresIn", "generateAccessToken", "data", "verifyToken", "logout", "clearTokens", "isHost", "console", "initializeAuth", "initialize", "getAccessToken", "isTokenExpired", "catch"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/shared/hooks/api/useAuthApi.ts"], "sourcesContent": ["// Authentication API hook\r\nimport { useCallback } from 'react';\r\nimport { useAppDispatch, useAppSelector } from '../../../app/store';\r\nimport { \r\n  loginUser, \r\n  refreshAccessToken, \r\n  logoutUser, \r\n  setTokens, \r\n  clearAuth \r\n} from '../../../app/store/slices/authSlice';\r\nimport { authApi } from '../../services/auth/authApi';\r\nimport { tokenService } from '../../services/auth/tokenService';\r\nimport { LoginRequest } from '../../types';\r\n\r\nexport const useAuthApi = () => {\r\n  const dispatch = useAppDispatch();\r\n  const { isLoading, error, isAuthenticated, user } = useAppSelector(state => state.auth);\r\n\r\n  /**\r\n   * Login user\r\n   */\r\n  const login = useCallback(async (credentials: LoginRequest) => {\r\n    try {\r\n      const result = await dispatch(loginUser(credentials)).unwrap();\r\n      \r\n      // Set tokens in token service\r\n      tokenService.setTokens(\r\n        result.accessToken,\r\n        result.refreshToken,\r\n        result.expiresIn\r\n      );\r\n      \r\n      return result;\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  }, [dispatch]);\r\n\r\n  /**\r\n   * Generate access token for room-based auth\r\n   */\r\n  const generateAccessToken = useCallback(async (data: { roomId: string; role: string }) => {\r\n    try {\r\n      const result = await authApi.generateAccessToken(data);\r\n      \r\n      // Set tokens in Redux and token service\r\n      dispatch(setTokens({\r\n        accessToken: result.accessToken,\r\n        refreshToken: result.refreshToken,\r\n        expiresIn: 30 * 60, // 30 minutes default\r\n      }));\r\n      \r\n      return result;\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  }, [dispatch]);\r\n\r\n  /**\r\n   * Verify current token\r\n   */\r\n  const verifyToken = useCallback(async () => {\r\n    try {\r\n      const result = await authApi.verifyToken();\r\n      return result;\r\n    } catch (error) {\r\n      // Token is invalid, clear auth state\r\n      dispatch(clearAuth());\r\n      throw error;\r\n    }\r\n  }, [dispatch]);\r\n\r\n  /**\r\n   * Refresh access token\r\n   */\r\n  const refreshToken = useCallback(async () => {\r\n    try {\r\n      const result = await dispatch(refreshAccessToken()).unwrap();\r\n      return result;\r\n    } catch (error) {\r\n      // Refresh failed, clear auth state\r\n      dispatch(clearAuth());\r\n      throw error;\r\n    }\r\n  }, [dispatch]);\r\n\r\n  /**\r\n   * Logout user\r\n   */\r\n  const logout = useCallback(async () => {\r\n    try {\r\n      await dispatch(logoutUser()).unwrap();\r\n      tokenService.clearTokens();\r\n    } catch (error) {\r\n      // Even if logout fails, clear local state\r\n      dispatch(clearAuth());\r\n      tokenService.clearTokens();\r\n      throw error;\r\n    }\r\n  }, [dispatch]);\r\n\r\n  /**\r\n   * Check if user is host (legacy compatibility)\r\n   */\r\n  const isHost = useCallback(async (data: any) => {\r\n    try {\r\n      const result = await authApi.isHost(data);\r\n      return result;\r\n    } catch (error) {\r\n      console.error('Failed to check host status:', error);\r\n      return false;\r\n    }\r\n  }, []);\r\n\r\n  /**\r\n   * Initialize authentication (call on app startup)\r\n   */\r\n  const initializeAuth = useCallback(() => {\r\n    // Initialize token service\r\n    tokenService.initialize();\r\n    \r\n    // Check if we have valid tokens\r\n    const accessToken = tokenService.getAccessToken();\r\n    if (accessToken && !tokenService.isTokenExpired()) {\r\n      // Verify token with backend\r\n      verifyToken().catch(() => {\r\n        // Token verification failed, clear auth\r\n        dispatch(clearAuth());\r\n      });\r\n    } else {\r\n      // No valid token, clear auth state\r\n      dispatch(clearAuth());\r\n    }\r\n  }, [dispatch, verifyToken]);\r\n\r\n  return {\r\n    // State\r\n    isLoading,\r\n    error,\r\n    isAuthenticated,\r\n    user,\r\n    \r\n    // Actions\r\n    login,\r\n    generateAccessToken,\r\n    verifyToken,\r\n    refreshToken,\r\n    logout,\r\n    isHost,\r\n    initializeAuth,\r\n  };\r\n};\r\n\r\nexport default useAuthApi;\r\n"], "mappings": ";AAAA;AACA,SAASA,WAAW,QAAQ,OAAO;AACnC,SAASC,cAAc,EAAEC,cAAc,QAAQ,oBAAoB;AACnE,SACEC,SAAS,EACTC,kBAAkB,EAClBC,UAAU,EACVC,SAAS,EACTC,SAAS,QACJ,qCAAqC;AAC5C,SAASC,OAAO,QAAQ,6BAA6B;AACrD,SAASC,YAAY,QAAQ,kCAAkC;AAG/D,OAAO,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAMC,QAAQ,GAAGX,cAAc,CAAC,CAAC;EACjC,MAAM;IAAEY,SAAS;IAAEC,KAAK;IAAEC,eAAe;IAAEC;EAAK,CAAC,GAAGd,cAAc,CAACe,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC;;EAEvF;AACF;AACA;EACE,MAAMC,KAAK,GAAGnB,WAAW,CAAC,MAAOoB,WAAyB,IAAK;IAC7D,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMT,QAAQ,CAACT,SAAS,CAACiB,WAAW,CAAC,CAAC,CAACE,MAAM,CAAC,CAAC;;MAE9D;MACAb,YAAY,CAACH,SAAS,CACpBe,MAAM,CAACE,WAAW,EAClBF,MAAM,CAACG,YAAY,EACnBH,MAAM,CAACI,SACT,CAAC;MAED,OAAOJ,MAAM;IACf,CAAC,CAAC,OAAOP,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC,EAAE,CAACF,QAAQ,CAAC,CAAC;;EAEd;AACF;AACA;EACE,MAAMc,mBAAmB,GAAG1B,WAAW,CAAC,MAAO2B,IAAsC,IAAK;IACxF,IAAI;MACF,MAAMN,MAAM,GAAG,MAAMb,OAAO,CAACkB,mBAAmB,CAACC,IAAI,CAAC;;MAEtD;MACAf,QAAQ,CAACN,SAAS,CAAC;QACjBiB,WAAW,EAAEF,MAAM,CAACE,WAAW;QAC/BC,YAAY,EAAEH,MAAM,CAACG,YAAY;QACjCC,SAAS,EAAE,EAAE,GAAG,EAAE,CAAE;MACtB,CAAC,CAAC,CAAC;MAEH,OAAOJ,MAAM;IACf,CAAC,CAAC,OAAOP,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC,EAAE,CAACF,QAAQ,CAAC,CAAC;;EAEd;AACF;AACA;EACE,MAAMgB,WAAW,GAAG5B,WAAW,CAAC,YAAY;IAC1C,IAAI;MACF,MAAMqB,MAAM,GAAG,MAAMb,OAAO,CAACoB,WAAW,CAAC,CAAC;MAC1C,OAAOP,MAAM;IACf,CAAC,CAAC,OAAOP,KAAK,EAAE;MACd;MACAF,QAAQ,CAACL,SAAS,CAAC,CAAC,CAAC;MACrB,MAAMO,KAAK;IACb;EACF,CAAC,EAAE,CAACF,QAAQ,CAAC,CAAC;;EAEd;AACF;AACA;EACE,MAAMY,YAAY,GAAGxB,WAAW,CAAC,YAAY;IAC3C,IAAI;MACF,MAAMqB,MAAM,GAAG,MAAMT,QAAQ,CAACR,kBAAkB,CAAC,CAAC,CAAC,CAACkB,MAAM,CAAC,CAAC;MAC5D,OAAOD,MAAM;IACf,CAAC,CAAC,OAAOP,KAAK,EAAE;MACd;MACAF,QAAQ,CAACL,SAAS,CAAC,CAAC,CAAC;MACrB,MAAMO,KAAK;IACb;EACF,CAAC,EAAE,CAACF,QAAQ,CAAC,CAAC;;EAEd;AACF;AACA;EACE,MAAMiB,MAAM,GAAG7B,WAAW,CAAC,YAAY;IACrC,IAAI;MACF,MAAMY,QAAQ,CAACP,UAAU,CAAC,CAAC,CAAC,CAACiB,MAAM,CAAC,CAAC;MACrCb,YAAY,CAACqB,WAAW,CAAC,CAAC;IAC5B,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACd;MACAF,QAAQ,CAACL,SAAS,CAAC,CAAC,CAAC;MACrBE,YAAY,CAACqB,WAAW,CAAC,CAAC;MAC1B,MAAMhB,KAAK;IACb;EACF,CAAC,EAAE,CAACF,QAAQ,CAAC,CAAC;;EAEd;AACF;AACA;EACE,MAAMmB,MAAM,GAAG/B,WAAW,CAAC,MAAO2B,IAAS,IAAK;IAC9C,IAAI;MACF,MAAMN,MAAM,GAAG,MAAMb,OAAO,CAACuB,MAAM,CAACJ,IAAI,CAAC;MACzC,OAAON,MAAM;IACf,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdkB,OAAO,CAAClB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,OAAO,KAAK;IACd;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;AACF;AACA;EACE,MAAMmB,cAAc,GAAGjC,WAAW,CAAC,MAAM;IACvC;IACAS,YAAY,CAACyB,UAAU,CAAC,CAAC;;IAEzB;IACA,MAAMX,WAAW,GAAGd,YAAY,CAAC0B,cAAc,CAAC,CAAC;IACjD,IAAIZ,WAAW,IAAI,CAACd,YAAY,CAAC2B,cAAc,CAAC,CAAC,EAAE;MACjD;MACAR,WAAW,CAAC,CAAC,CAACS,KAAK,CAAC,MAAM;QACxB;QACAzB,QAAQ,CAACL,SAAS,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACAK,QAAQ,CAACL,SAAS,CAAC,CAAC,CAAC;IACvB;EACF,CAAC,EAAE,CAACK,QAAQ,EAAEgB,WAAW,CAAC,CAAC;EAE3B,OAAO;IACL;IACAf,SAAS;IACTC,KAAK;IACLC,eAAe;IACfC,IAAI;IAEJ;IACAG,KAAK;IACLO,mBAAmB;IACnBE,WAAW;IACXJ,YAAY;IACZK,MAAM;IACNE,MAAM;IACNE;EACF,CAAC;AACH,CAAC;AAACtB,EAAA,CAzIWD,UAAU;EAAA,QACJT,cAAc,EACqBC,cAAc;AAAA;AAyIpE,eAAeQ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}