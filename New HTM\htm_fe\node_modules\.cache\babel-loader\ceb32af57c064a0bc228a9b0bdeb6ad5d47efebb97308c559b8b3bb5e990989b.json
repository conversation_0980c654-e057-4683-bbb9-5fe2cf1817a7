{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\New HTM\\\\htm_fe\\\\src\\\\components\\\\HostQuestionPreview.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useHost } from '../context/hostContext';\nimport { usePlayer } from '../context/playerContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HostQuestionPreview = () => {\n  _s();\n  const {\n    currentAnswer,\n    prefetchedQuestion,\n    prefetchedAnswer,\n    showCurrentAnswer,\n    currentQuestionIndex\n  } = useHost();\n  const {\n    currentQuestion\n  } = usePlayer();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-4 p-4 bg-slate-800/50 rounded-lg border border-blue-400/30\",\n    children: [prefetchedQuestion && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-blue-600/20 border border-blue-400/50 rounded-lg p-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-blue-300 font-semibold mb-2 flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"w-2 h-2 bg-blue-400 rounded-full mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 13\n        }, this), \"C\\xE2u h\\u1ECFi ti\\u1EBFp theo (C\\xE2u \", prefetchedQuestion.stt, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-blue-100 text-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium\",\n            children: [\"C\\xE2u \", prefetchedQuestion.stt, \":\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 15\n          }, this), \" \", prefetchedQuestion.question]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 13\n        }, this), prefetchedQuestion.imgUrl && /*#__PURE__*/_jsxDEV(\"img\", {\n          src: prefetchedQuestion.imgUrl,\n          alt: \"Next Question\",\n          className: \"max-w-xs rounded mt-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 15\n        }, this), prefetchedQuestion.packetName && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-blue-200/70 text-xs\",\n          children: [\"Ch\\u1EE7 \\u0111\\u1EC1: \", prefetchedQuestion.packetName]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 15\n        }, this), prefetchedQuestion.difficulty && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-blue-200/70 text-xs\",\n          children: [\"\\u0110\\u1ED9 kh\\xF3: \", prefetchedQuestion.difficulty]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 15\n        }, this), prefetchedAnswer && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-3 p-2 bg-blue-700/30 rounded border border-blue-500/30\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-blue-200 text-xs font-medium mb-1\",\n            children: \"\\uD83C\\uDFAF \\u0110\\xE1p \\xE1n:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-blue-100 text-sm font-medium\",\n            children: prefetchedAnswer\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 9\n    }, this), !prefetchedQuestion && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-blue-300/60 text-center py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-lg mb-2\",\n        children: \"\\uD83C\\uDFAE S\\u1EB5n s\\xE0ng b\\u1EAFt \\u0111\\u1EA7u!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm\",\n        children: \"Nh\\u1EA5n \\\"C\\xE2u h\\u1ECFi ti\\u1EBFp theo\\\" \\u0111\\u1EC3 hi\\u1EC3n th\\u1ECB c\\xE2u h\\u1ECFi \\u0111\\u1EA7u ti\\xEAn\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 9\n    }, this), prefetchedQuestion && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-blue-300/60 text-xs\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"\\uD83D\\uDCA1 \\u0110\\xE1p \\xE1n hi\\u1EC3n th\\u1ECB ngay b\\xEAn d\\u01B0\\u1EDBi c\\xE2u h\\u1ECFi trong khung ch\\xEDnh\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"\\uD83D\\uDD2E C\\xE2u h\\u1ECFi ti\\u1EBFp theo \\u0111\\u01B0\\u1EE3c t\\u1EA3i s\\u1EB5n \\u0111\\u1EC3 lu\\u1ED3ng game m\\u01B0\\u1EE3t m\\xE0 h\\u01A1n\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this);\n};\n_s(HostQuestionPreview, \"RRwP/Dpk+EkEUC/gViJFJvwKLe4=\", false, function () {\n  return [useHost, usePlayer];\n});\n_c = HostQuestionPreview;\nexport default HostQuestionPreview;\nvar _c;\n$RefreshReg$(_c, \"HostQuestionPreview\");", "map": {"version": 3, "names": ["React", "useHost", "usePlayer", "jsxDEV", "_jsxDEV", "HostQuestionPreview", "_s", "currentAnswer", "prefetchedQuestion", "prefetchedAnswer", "showCurrentAnswer", "currentQuestionIndex", "currentQuestion", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "stt", "question", "imgUrl", "src", "alt", "packetName", "difficulty", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/components/HostQuestionPreview.tsx"], "sourcesContent": ["import React from 'react';\nimport { useHost } from '../context/hostContext';\nimport { usePlayer } from '../context/playerContext';\n\nconst HostQuestionPreview: React.FC = () => {\n  const { currentAnswer, prefetchedQuestion, prefetchedAnswer, showCurrentAnswer, currentQuestionIndex } = useHost();\n  const { currentQuestion } = usePlayer();\n\n  return (\n    <div className=\"space-y-4 p-4 bg-slate-800/50 rounded-lg border border-blue-400/30\">\n      {/* Prefetched Question Preview */}\n      {prefetchedQuestion && (\n        <div className=\"bg-blue-600/20 border border-blue-400/50 rounded-lg p-4\">\n          <h3 className=\"text-blue-300 font-semibold mb-2 flex items-center\">\n            <span className=\"w-2 h-2 bg-blue-400 rounded-full mr-2\"></span>\n            Câu hỏi tiếp theo (Câu {prefetchedQuestion.stt})\n          </h3>\n          <div className=\"space-y-2\">\n            <p className=\"text-blue-100 text-sm\">\n              <span className=\"font-medium\">Câu {prefetchedQuestion.stt}:</span> {prefetchedQuestion.question}\n            </p>\n            {prefetchedQuestion.imgUrl && (\n              <img src={prefetchedQuestion.imgUrl} alt=\"Next Question\" className=\"max-w-xs rounded mt-2\" />\n            )}\n            {prefetchedQuestion.packetName && (\n              <p className=\"text-blue-200/70 text-xs\">\n                Chủ đề: {prefetchedQuestion.packetName}\n              </p>\n            )}\n            {prefetchedQuestion.difficulty && (\n              <p className=\"text-blue-200/70 text-xs\">\n                Độ khó: {prefetchedQuestion.difficulty}\n              </p>\n            )}\n            {prefetchedAnswer && (\n              <div className=\"mt-3 p-2 bg-blue-700/30 rounded border border-blue-500/30\">\n                <p className=\"text-blue-200 text-xs font-medium mb-1\">🎯 Đáp án:</p>\n                <p className=\"text-blue-100 text-sm font-medium\">{prefetchedAnswer}</p>\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n\n      {/* Instructions */}\n      {!prefetchedQuestion && (\n        <div className=\"text-blue-300/60 text-center py-8\">\n          <p className=\"text-lg mb-2\">🎮 Sẵn sàng bắt đầu!</p>\n          <p className=\"text-sm\">Nhấn \"Câu hỏi tiếp theo\" để hiển thị câu hỏi đầu tiên</p>\n        </div>\n      )}\n\n      {prefetchedQuestion && (\n        <div className=\"text-blue-300/60 text-xs\">\n          <p>💡 Đáp án hiển thị ngay bên dưới câu hỏi trong khung chính</p>\n          <p>🔮 Câu hỏi tiếp theo được tải sẵn để luồng game mượt mà hơn</p>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default HostQuestionPreview;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,SAAS,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,mBAA6B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1C,MAAM;IAAEC,aAAa;IAAEC,kBAAkB;IAAEC,gBAAgB;IAAEC,iBAAiB;IAAEC;EAAqB,CAAC,GAAGV,OAAO,CAAC,CAAC;EAClH,MAAM;IAAEW;EAAgB,CAAC,GAAGV,SAAS,CAAC,CAAC;EAEvC,oBACEE,OAAA;IAAKS,SAAS,EAAC,oEAAoE;IAAAC,QAAA,GAEhFN,kBAAkB,iBACjBJ,OAAA;MAAKS,SAAS,EAAC,yDAAyD;MAAAC,QAAA,gBACtEV,OAAA;QAAIS,SAAS,EAAC,oDAAoD;QAAAC,QAAA,gBAChEV,OAAA;UAAMS,SAAS,EAAC;QAAuC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,2CACxC,EAACV,kBAAkB,CAACW,GAAG,EAAC,GACjD;MAAA;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLd,OAAA;QAAKS,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBV,OAAA;UAAGS,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBAClCV,OAAA;YAAMS,SAAS,EAAC,aAAa;YAAAC,QAAA,GAAC,SAAI,EAACN,kBAAkB,CAACW,GAAG,EAAC,GAAC;UAAA;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAACV,kBAAkB,CAACY,QAAQ;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9F,CAAC,EACHV,kBAAkB,CAACa,MAAM,iBACxBjB,OAAA;UAAKkB,GAAG,EAAEd,kBAAkB,CAACa,MAAO;UAACE,GAAG,EAAC,eAAe;UAACV,SAAS,EAAC;QAAuB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAC7F,EACAV,kBAAkB,CAACgB,UAAU,iBAC5BpB,OAAA;UAAGS,SAAS,EAAC,0BAA0B;UAAAC,QAAA,GAAC,yBAC9B,EAACN,kBAAkB,CAACgB,UAAU;QAAA;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CACJ,EACAV,kBAAkB,CAACiB,UAAU,iBAC5BrB,OAAA;UAAGS,SAAS,EAAC,0BAA0B;UAAAC,QAAA,GAAC,uBAC9B,EAACN,kBAAkB,CAACiB,UAAU;QAAA;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CACJ,EACAT,gBAAgB,iBACfL,OAAA;UAAKS,SAAS,EAAC,2DAA2D;UAAAC,QAAA,gBACxEV,OAAA;YAAGS,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACpEd,OAAA;YAAGS,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAEL;UAAgB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGA,CAACV,kBAAkB,iBAClBJ,OAAA;MAAKS,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDV,OAAA;QAAGS,SAAS,EAAC,cAAc;QAAAC,QAAA,EAAC;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACpDd,OAAA;QAAGS,SAAS,EAAC,SAAS;QAAAC,QAAA,EAAC;MAAqD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7E,CACN,EAEAV,kBAAkB,iBACjBJ,OAAA;MAAKS,SAAS,EAAC,0BAA0B;MAAAC,QAAA,gBACvCV,OAAA;QAAAU,QAAA,EAAG;MAA0D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACjEd,OAAA;QAAAU,QAAA,EAAG;MAA2D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/D,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACZ,EAAA,CAxDID,mBAA6B;EAAA,QACwEJ,OAAO,EACpFC,SAAS;AAAA;AAAAwB,EAAA,GAFjCrB,mBAA6B;AA0DnC,eAAeA,mBAAmB;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}