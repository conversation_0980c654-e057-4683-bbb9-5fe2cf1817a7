{"name": "react-query", "version": "3.39.3", "description": "Hooks for managing, caching and syncing asynchronous and remote data in React", "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "repository": "tanner<PERSON>ley/react-query", "homepage": "https://github.com/tannerlinsley/react-query#readme", "funding": {"type": "github", "url": "https://github.com/sponsors/tanner<PERSON>ley"}, "main": "lib/index.js", "unpkg": "dist/react-query.development.js", "types": "types/index.d.ts", "module": "es/index.js", "sideEffects": ["es/index.js", "es/react/index.js", "es/react/setBatchUpdatesFn.js", "es/react/setLogger.js", "lib/index.js", "lib/react/index.js", "lib/react/setBatchUpdatesFn.js", "lib/react/setLogger.js"], "scripts": {"test": "is-ci \"test:ci\" \"test:dev\"", "test:dev": "npm run test:types && npm run test:format && npm run test:eslint && jest --watch", "test:ci": "npm run test:types && npm run test:format && npm run test:eslint && jest", "test:coverage": "yarn test:ci; open coverage/lcov-report/index.html", "test:format": "yarn prettier --check", "test:types": "tsc", "test:eslint": "eslint --ext .ts,.tsx ./src", "test:size": "yarn build:umd && bundlewatch", "build": "yarn build:commonjs && yarn build:es && yarn build:umd && yarn build:types", "build:commonjs": "rimraf ./lib && cross-env BABEL_ENV=commonjs babel --extensions .ts,.tsx --ignore ./src/**/tests/**/* ./src --out-dir lib", "build:es": "rimraf ./es && babel --extensions .ts,.tsx --ignore ./src/**/tests/**/* ./src --out-dir es", "build:umd": "rimraf ./dist && cross-env NODE_ENV=production rollup -c && rollup-plugin-visualizer stats-react.json", "build:types": "rimraf ./types && tsc --project ./tsconfig.types.json && replace 'import type' 'import' ./types -r --silent && replace 'export type' 'export' ./types -r --silent", "watch": "yarn watch:commonjs & yarn watch:es & yarn watch:umd & yarn watch:types", "watch:commonjs": "rimraf ./lib && cross-env BABEL_ENV=commonjs babel --watch --extensions .ts,.tsx --ignore ./src/**/tests/**/* ./src --out-dir lib", "watch:es": "rimraf ./es && babel --watch --extensions .ts,.tsx --ignore ./src/**/tests/**/* ./src --out-dir es", "watch:umd": "rimraf ./dist && cross-env NODE_ENV=production rollup -w -c && rollup-plugin-visualizer stats-react.json", "watch:types": "rimraf ./types && tsc --watch --project ./tsconfig.types.json && replace 'import type' 'import' ./types -r --silent && replace 'export type' 'export' ./types -r --silent", "now-build": "yarn && cd www && yarn && yarn build", "prettier": "prettier \"{.,src,src/**,example/src,example/src/**,types}/*.{md,js,jsx,ts,tsx,json}\"", "start": "yarn watch", "format": "yarn prettier --write", "stats": "open ./stats.html"}, "files": ["core", "dist", "es", "hydration", "devtools", "persistQueryClient-experimental", "createWebStoragePersistor-experimental", "createAsyncStoragePersistor-experimental", "broadcastQueryClient-experimental", "lib", "react", "scripts", "types"], "dependencies": {"@babel/runtime": "^7.5.5", "broadcast-channel": "^3.4.1", "match-sorter": "^6.0.2"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0"}, "peerDependenciesMeta": {"react-dom": {"optional": true}, "react-native": {"optional": true}}, "typesVersions": {"<4.1": {"types/*": ["types/ts3.8/*"]}}, "devDependencies": {"@babel/cli": "^7.11.6", "@babel/core": "^7.11.6", "@babel/plugin-transform-runtime": "^7.11.5", "@babel/preset-env": "^7.11.5", "@babel/preset-react": "^7.10.4", "@babel/preset-typescript": "^7.10.4", "@rollup/plugin-replace": "^2.3.3", "@svgr/rollup": "^5.4.0", "@testing-library/react": "^10.4.7", "@testing-library/jest-dom": "^5.14.1", "@types/jest": "^26.0.4", "@types/react": "^16.9.41", "@types/react-dom": "^16.9.8", "@typescript-eslint/eslint-plugin": "^4.8.1", "@typescript-eslint/parser": "^4.8.1", "babel-eslint": "^10.1.0", "babel-jest": "^26.0.1", "babel-plugin-const-enum": "^1.0.1", "babel-plugin-transform-async-to-promises": "^0.8.15", "bundlewatch": "^0.3.2", "cross-env": "^7.0.2", "eslint": "7.x", "eslint-config-prettier": "^6.11.0", "eslint-config-react-app": "^5.2.1", "eslint-config-standard": "^14.1.1", "eslint-config-standard-react": "^9.2.0", "eslint-plugin-flowtype": "5.x", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jsx-a11y": "6.x", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-react": "7.20.0", "eslint-plugin-react-hooks": "4.2.0", "eslint-plugin-standard": "^4.0.1", "is-ci-cli": "^2.1.1", "jest": "^26.0.1", "prettier": "2.2.1", "react": "^16.13.0", "react-dom": "^16.13.1", "react-error-boundary": "^2.2.2", "replace": "^1.2.0", "rimraf": "^3.0.2", "rollup": "^2.16.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-jscc": "^1.0.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-peer-deps-external": "^2.2.2", "rollup-plugin-prettier": "^2.1.0", "rollup-plugin-size": "^0.2.2", "rollup-plugin-terser": "^6.1.0", "rollup-plugin-visualizer": "^4.0.4", "type-fest": "^0.21.0", "typescript": "^4.1.2"}, "resolutions": {"@types/react": "^16.9.41", "@types/react-dom": "^16.9.8"}, "release": {"branches": ["main"]}, "bundlewatch": {"files": [{"path": "dist/*.production.min.js"}]}}