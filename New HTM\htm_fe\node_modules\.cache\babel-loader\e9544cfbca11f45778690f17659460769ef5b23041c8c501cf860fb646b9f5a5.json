{"ast": null, "code": "// Game-related constants\nexport const ROUNDS = {\n  ROUND_1: 1,\n  ROUND_2: 2,\n  ROUND_3: 3,\n  ROUND_4: 4\n};\nexport const GAME_MODES = {\n  MANUAL: 'manual',\n  AUTO: 'auto',\n  ADAPTIVE: 'adaptive'\n};\nexport const DIFFICULTIES = {\n  EASY: 'easy',\n  MEDIUM: 'medium',\n  HARD: 'hard'\n};\nexport const ROUND_4_DIFFICULTIES = {\n  EASY: {\n    range: [1, 20],\n    points: 20\n  },\n  MEDIUM: {\n    range: [21, 40],\n    points: 25\n  },\n  HARD: {\n    range: [41, 60],\n    points: 30\n  }\n};\nexport const DEFAULT_SCORE_RULES = {\n  round1: [20, 15, 10, 5],\n  round2: [20, 15, 10, 5],\n  round3: [10, 10, 10, 10],\n  round4: [20, 25, 30]\n};\nexport const MAX_PLAYERS = 8;\nexport const MIN_PLAYERS = 2;\nexport const DEFAULT_TIME_LIMIT = 30; // seconds\n\nexport const ROUND_2_GRID_SIZE = {\n  ROWS: 7,\n  COLS: 15\n};\nexport const ROUND_4_GRID_SIZE = {\n  ROWS: 5,\n  COLS: 5\n};\nexport const QUESTION_TYPES = {\n  TEXT: 'text',\n  IMAGE: 'image',\n  AUDIO: 'audio'\n};\nexport const PLAYER_ROLES = {\n  PLAYER: 'player',\n  SPECTATOR: 'spectator',\n  HOST: 'host'\n};\nexport const GAME_EVENTS = {\n  QUESTION_START: 'question_start',\n  QUESTION_END: 'question_end',\n  ROUND_START: 'round_start',\n  ROUND_END: 'round_end',\n  GAME_START: 'game_start',\n  GAME_END: 'game_end',\n  PLAYER_ANSWER: 'player_answer',\n  SCORE_UPDATE: 'score_update',\n  PLAYER_JOINED: 'player_joined',\n  PLAYER_LEFT: 'player_left'\n};\nexport const FLASH_DURATION = 3000; // 3 seconds\n\nexport const BUZZ_TIMEOUT = 4000; // 4 seconds for Round 4 buzz\n\nexport const TOKEN_REFRESH_INTERVAL = 20 * 60 * 1000; // 20 minutes", "map": {"version": 3, "names": ["ROUNDS", "ROUND_1", "ROUND_2", "ROUND_3", "ROUND_4", "GAME_MODES", "MANUAL", "AUTO", "ADAPTIVE", "DIFFICULTIES", "EASY", "MEDIUM", "HARD", "ROUND_4_DIFFICULTIES", "range", "points", "DEFAULT_SCORE_RULES", "round1", "round2", "round3", "round4", "MAX_PLAYERS", "MIN_PLAYERS", "DEFAULT_TIME_LIMIT", "ROUND_2_GRID_SIZE", "ROWS", "COLS", "ROUND_4_GRID_SIZE", "QUESTION_TYPES", "TEXT", "IMAGE", "AUDIO", "PLAYER_ROLES", "PLAYER", "SPECTATOR", "HOST", "GAME_EVENTS", "QUESTION_START", "QUESTION_END", "ROUND_START", "ROUND_END", "GAME_START", "GAME_END", "PLAYER_ANSWER", "SCORE_UPDATE", "PLAYER_JOINED", "PLAYER_LEFT", "FLASH_DURATION", "BUZZ_TIMEOUT", "TOKEN_REFRESH_INTERVAL"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/shared/constants/game.constants.ts"], "sourcesContent": ["// Game-related constants\r\nexport const ROUNDS = {\r\n  ROUND_1: 1,\r\n  ROUND_2: 2,\r\n  ROUND_3: 3,\r\n  ROUND_4: 4,\r\n} as const;\r\n\r\nexport const GAME_MODES = {\r\n  MANUAL: 'manual',\r\n  AUTO: 'auto',\r\n  ADAPTIVE: 'adaptive',\r\n} as const;\r\n\r\nexport const DIFFICULTIES = {\r\n  EASY: 'easy',\r\n  MEDIUM: 'medium',\r\n  HARD: 'hard',\r\n} as const;\r\n\r\nexport const ROUND_4_DIFFICULTIES = {\r\n  EASY: { range: [1, 20], points: 20 },\r\n  MEDIUM: { range: [21, 40], points: 25 },\r\n  HARD: { range: [41, 60], points: 30 },\r\n} as const;\r\n\r\nexport const DEFAULT_SCORE_RULES = {\r\n  round1: [20, 15, 10, 5],\r\n  round2: [20, 15, 10, 5],\r\n  round3: [10, 10, 10, 10],\r\n  round4: [20, 25, 30],\r\n} as const;\r\n\r\nexport const MAX_PLAYERS = 8;\r\nexport const MIN_PLAYERS = 2;\r\n\r\nexport const DEFAULT_TIME_LIMIT = 30; // seconds\r\n\r\nexport const ROUND_2_GRID_SIZE = {\r\n  ROWS: 7,\r\n  COLS: 15,\r\n} as const;\r\n\r\nexport const ROUND_4_GRID_SIZE = {\r\n  ROWS: 5,\r\n  COLS: 5,\r\n} as const;\r\n\r\nexport const QUESTION_TYPES = {\r\n  TEXT: 'text',\r\n  IMAGE: 'image',\r\n  AUDIO: 'audio',\r\n} as const;\r\n\r\nexport const PLAYER_ROLES = {\r\n  PLAYER: 'player',\r\n  SPECTATOR: 'spectator',\r\n  HOST: 'host',\r\n} as const;\r\n\r\nexport const GAME_EVENTS = {\r\n  QUESTION_START: 'question_start',\r\n  QUESTION_END: 'question_end',\r\n  ROUND_START: 'round_start',\r\n  ROUND_END: 'round_end',\r\n  GAME_START: 'game_start',\r\n  GAME_END: 'game_end',\r\n  PLAYER_ANSWER: 'player_answer',\r\n  SCORE_UPDATE: 'score_update',\r\n  PLAYER_JOINED: 'player_joined',\r\n  PLAYER_LEFT: 'player_left',\r\n} as const;\r\n\r\nexport const FLASH_DURATION = 3000; // 3 seconds\r\n\r\nexport const BUZZ_TIMEOUT = 4000; // 4 seconds for Round 4 buzz\r\n\r\nexport const TOKEN_REFRESH_INTERVAL = 20 * 60 * 1000; // 20 minutes\r\n"], "mappings": "AAAA;AACA,OAAO,MAAMA,MAAM,GAAG;EACpBC,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE;AACX,CAAU;AAEV,OAAO,MAAMC,UAAU,GAAG;EACxBC,MAAM,EAAE,QAAQ;EAChBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE;AACZ,CAAU;AAEV,OAAO,MAAMC,YAAY,GAAG;EAC1BC,IAAI,EAAE,MAAM;EACZC,MAAM,EAAE,QAAQ;EAChBC,IAAI,EAAE;AACR,CAAU;AAEV,OAAO,MAAMC,oBAAoB,GAAG;EAClCH,IAAI,EAAE;IAAEI,KAAK,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAG,CAAC;EACpCJ,MAAM,EAAE;IAAEG,KAAK,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAG,CAAC;EACvCH,IAAI,EAAE;IAAEE,KAAK,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAG;AACtC,CAAU;AAEV,OAAO,MAAMC,mBAAmB,GAAG;EACjCC,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;EACvBC,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;EACvBC,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EACxBC,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE;AACrB,CAAU;AAEV,OAAO,MAAMC,WAAW,GAAG,CAAC;AAC5B,OAAO,MAAMC,WAAW,GAAG,CAAC;AAE5B,OAAO,MAAMC,kBAAkB,GAAG,EAAE,CAAC,CAAC;;AAEtC,OAAO,MAAMC,iBAAiB,GAAG;EAC/BC,IAAI,EAAE,CAAC;EACPC,IAAI,EAAE;AACR,CAAU;AAEV,OAAO,MAAMC,iBAAiB,GAAG;EAC/BF,IAAI,EAAE,CAAC;EACPC,IAAI,EAAE;AACR,CAAU;AAEV,OAAO,MAAME,cAAc,GAAG;EAC5BC,IAAI,EAAE,MAAM;EACZC,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE;AACT,CAAU;AAEV,OAAO,MAAMC,YAAY,GAAG;EAC1BC,MAAM,EAAE,QAAQ;EAChBC,SAAS,EAAE,WAAW;EACtBC,IAAI,EAAE;AACR,CAAU;AAEV,OAAO,MAAMC,WAAW,GAAG;EACzBC,cAAc,EAAE,gBAAgB;EAChCC,YAAY,EAAE,cAAc;EAC5BC,WAAW,EAAE,aAAa;EAC1BC,SAAS,EAAE,WAAW;EACtBC,UAAU,EAAE,YAAY;EACxBC,QAAQ,EAAE,UAAU;EACpBC,aAAa,EAAE,eAAe;EAC9BC,YAAY,EAAE,cAAc;EAC5BC,aAAa,EAAE,eAAe;EAC9BC,WAAW,EAAE;AACf,CAAU;AAEV,OAAO,MAAMC,cAAc,GAAG,IAAI,CAAC,CAAC;;AAEpC,OAAO,MAAMC,YAAY,GAAG,IAAI,CAAC,CAAC;;AAElC,OAAO,MAAMC,sBAAsB,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}