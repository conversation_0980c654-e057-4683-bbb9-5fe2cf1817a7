{"ast": null, "code": "// Central export for all constants\nexport * from './api.constants';\nexport * from './game.constants';", "map": {"version": 3, "names": [], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/shared/constants/index.ts"], "sourcesContent": ["// Central export for all constants\r\nexport * from './api.constants';\r\nexport * from './game.constants';\r\n"], "mappings": "AAAA;AACA,cAAc,iBAAiB;AAC/B,cAAc,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}