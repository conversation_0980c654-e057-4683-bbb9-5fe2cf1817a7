{"ast": null, "code": "var _s = $RefreshSig$();\n// Local storage hook with TypeScript support\nimport { useState, useEffect, useCallback } from 'react';\nexport const useLocalStorage = (key, initialValue) => {\n  _s();\n  // Get from local storage then parse stored json or return initialValue\n  const [storedValue, setStoredValue] = useState(() => {\n    try {\n      const item = window.localStorage.getItem(key);\n      return item ? JSON.parse(item) : initialValue;\n    } catch (error) {\n      console.error(`Error reading localStorage key \"${key}\":`, error);\n      return initialValue;\n    }\n  });\n\n  // Return a wrapped version of useState's setter function that persists the new value to localStorage\n  const setValue = useCallback(value => {\n    try {\n      // Allow value to be a function so we have the same API as useState\n      const valueToStore = value instanceof Function ? value(storedValue) : value;\n\n      // Save state\n      setStoredValue(valueToStore);\n\n      // Save to local storage\n      window.localStorage.setItem(key, JSON.stringify(valueToStore));\n    } catch (error) {\n      console.error(`Error setting localStorage key \"${key}\":`, error);\n    }\n  }, [key, storedValue]);\n\n  // Remove from localStorage\n  const removeValue = useCallback(() => {\n    try {\n      window.localStorage.removeItem(key);\n      setStoredValue(initialValue);\n    } catch (error) {\n      console.error(`Error removing localStorage key \"${key}\":`, error);\n    }\n  }, [key, initialValue]);\n\n  // Listen for changes to this key from other tabs/windows\n  useEffect(() => {\n    const handleStorageChange = e => {\n      if (e.key === key && e.newValue !== null) {\n        try {\n          setStoredValue(JSON.parse(e.newValue));\n        } catch (error) {\n          console.error(`Error parsing localStorage key \"${key}\":`, error);\n        }\n      }\n    };\n    window.addEventListener('storage', handleStorageChange);\n    return () => {\n      window.removeEventListener('storage', handleStorageChange);\n    };\n  }, [key]);\n  return [storedValue, setValue, removeValue];\n};\n_s(useLocalStorage, \"JSSMftIu1lrSF/ni2BEgQvwEBrM=\");\nexport default useLocalStorage;", "map": {"version": 3, "names": ["useState", "useEffect", "useCallback", "useLocalStorage", "key", "initialValue", "_s", "storedValue", "setStoredValue", "item", "window", "localStorage", "getItem", "JSON", "parse", "error", "console", "setValue", "value", "valueToStore", "Function", "setItem", "stringify", "removeValue", "removeItem", "handleStorageChange", "e", "newValue", "addEventListener", "removeEventListener"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/shared/hooks/common/useLocalStorage.ts"], "sourcesContent": ["// Local storage hook with TypeScript support\r\nimport { useState, useEffect, useCallback } from 'react';\r\n\r\ntype SetValue<T> = T | ((val: T) => T);\r\n\r\nexport const useLocalStorage = <T>(\r\n  key: string,\r\n  initialValue: T\r\n): [T, (value: SetValue<T>) => void, () => void] => {\r\n  // Get from local storage then parse stored json or return initialValue\r\n  const [storedValue, setStoredValue] = useState<T>(() => {\r\n    try {\r\n      const item = window.localStorage.getItem(key);\r\n      return item ? JSON.parse(item) : initialValue;\r\n    } catch (error) {\r\n      console.error(`Error reading localStorage key \"${key}\":`, error);\r\n      return initialValue;\r\n    }\r\n  });\r\n\r\n  // Return a wrapped version of useState's setter function that persists the new value to localStorage\r\n  const setValue = useCallback(\r\n    (value: SetValue<T>) => {\r\n      try {\r\n        // Allow value to be a function so we have the same API as useState\r\n        const valueToStore = value instanceof Function ? value(storedValue) : value;\r\n        \r\n        // Save state\r\n        setStoredValue(valueToStore);\r\n        \r\n        // Save to local storage\r\n        window.localStorage.setItem(key, JSON.stringify(valueToStore));\r\n      } catch (error) {\r\n        console.error(`Error setting localStorage key \"${key}\":`, error);\r\n      }\r\n    },\r\n    [key, storedValue]\r\n  );\r\n\r\n  // Remove from localStorage\r\n  const removeValue = useCallback(() => {\r\n    try {\r\n      window.localStorage.removeItem(key);\r\n      setStoredValue(initialValue);\r\n    } catch (error) {\r\n      console.error(`Error removing localStorage key \"${key}\":`, error);\r\n    }\r\n  }, [key, initialValue]);\r\n\r\n  // Listen for changes to this key from other tabs/windows\r\n  useEffect(() => {\r\n    const handleStorageChange = (e: StorageEvent) => {\r\n      if (e.key === key && e.newValue !== null) {\r\n        try {\r\n          setStoredValue(JSON.parse(e.newValue));\r\n        } catch (error) {\r\n          console.error(`Error parsing localStorage key \"${key}\":`, error);\r\n        }\r\n      }\r\n    };\r\n\r\n    window.addEventListener('storage', handleStorageChange);\r\n    \r\n    return () => {\r\n      window.removeEventListener('storage', handleStorageChange);\r\n    };\r\n  }, [key]);\r\n\r\n  return [storedValue, setValue, removeValue];\r\n};\r\n\r\nexport default useLocalStorage;\r\n"], "mappings": ";AAAA;AACA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAIxD,OAAO,MAAMC,eAAe,GAAGA,CAC7BC,GAAW,EACXC,YAAe,KACmC;EAAAC,EAAA;EAClD;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGR,QAAQ,CAAI,MAAM;IACtD,IAAI;MACF,MAAMS,IAAI,GAAGC,MAAM,CAACC,YAAY,CAACC,OAAO,CAACR,GAAG,CAAC;MAC7C,OAAOK,IAAI,GAAGI,IAAI,CAACC,KAAK,CAACL,IAAI,CAAC,GAAGJ,YAAY;IAC/C,CAAC,CAAC,OAAOU,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mCAAmCX,GAAG,IAAI,EAAEW,KAAK,CAAC;MAChE,OAAOV,YAAY;IACrB;EACF,CAAC,CAAC;;EAEF;EACA,MAAMY,QAAQ,GAAGf,WAAW,CACzBgB,KAAkB,IAAK;IACtB,IAAI;MACF;MACA,MAAMC,YAAY,GAAGD,KAAK,YAAYE,QAAQ,GAAGF,KAAK,CAACX,WAAW,CAAC,GAAGW,KAAK;;MAE3E;MACAV,cAAc,CAACW,YAAY,CAAC;;MAE5B;MACAT,MAAM,CAACC,YAAY,CAACU,OAAO,CAACjB,GAAG,EAAES,IAAI,CAACS,SAAS,CAACH,YAAY,CAAC,CAAC;IAChE,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mCAAmCX,GAAG,IAAI,EAAEW,KAAK,CAAC;IAClE;EACF,CAAC,EACD,CAACX,GAAG,EAAEG,WAAW,CACnB,CAAC;;EAED;EACA,MAAMgB,WAAW,GAAGrB,WAAW,CAAC,MAAM;IACpC,IAAI;MACFQ,MAAM,CAACC,YAAY,CAACa,UAAU,CAACpB,GAAG,CAAC;MACnCI,cAAc,CAACH,YAAY,CAAC;IAC9B,CAAC,CAAC,OAAOU,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oCAAoCX,GAAG,IAAI,EAAEW,KAAK,CAAC;IACnE;EACF,CAAC,EAAE,CAACX,GAAG,EAAEC,YAAY,CAAC,CAAC;;EAEvB;EACAJ,SAAS,CAAC,MAAM;IACd,MAAMwB,mBAAmB,GAAIC,CAAe,IAAK;MAC/C,IAAIA,CAAC,CAACtB,GAAG,KAAKA,GAAG,IAAIsB,CAAC,CAACC,QAAQ,KAAK,IAAI,EAAE;QACxC,IAAI;UACFnB,cAAc,CAACK,IAAI,CAACC,KAAK,CAACY,CAAC,CAACC,QAAQ,CAAC,CAAC;QACxC,CAAC,CAAC,OAAOZ,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,mCAAmCX,GAAG,IAAI,EAAEW,KAAK,CAAC;QAClE;MACF;IACF,CAAC;IAEDL,MAAM,CAACkB,gBAAgB,CAAC,SAAS,EAAEH,mBAAmB,CAAC;IAEvD,OAAO,MAAM;MACXf,MAAM,CAACmB,mBAAmB,CAAC,SAAS,EAAEJ,mBAAmB,CAAC;IAC5D,CAAC;EACH,CAAC,EAAE,CAACrB,GAAG,CAAC,CAAC;EAET,OAAO,CAACG,WAAW,EAAEU,QAAQ,EAAEM,WAAW,CAAC;AAC7C,CAAC;AAACjB,EAAA,CAhEWH,eAAe;AAkE5B,eAAeA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}