{"ast": null, "code": "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M6 9H4.5a2.5 2.5 0 0 1 0-5H6\",\n  key: \"17hqa7\"\n}], [\"path\", {\n  d: \"M18 9h1.5a2.5 2.5 0 0 0 0-5H18\",\n  key: \"lmptdp\"\n}], [\"path\", {\n  d: \"M4 22h16\",\n  key: \"57wxv0\"\n}], [\"path\", {\n  d: \"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22\",\n  key: \"1nw9bq\"\n}], [\"path\", {\n  d: \"M14 14.66V17c0 .*********** 1.21C16.15 18.75 17 20.24 17 22\",\n  key: \"1np0yb\"\n}], [\"path\", {\n  d: \"M18 2H6v7a6 6 0 0 0 12 0V2Z\",\n  key: \"u46fv3\"\n}]];\nconst Trophy = createLucideIcon(\"trophy\", __iconNode);\nexport { __iconNode, Trophy as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Trophy", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\node_modules\\lucide-react\\src\\icons\\trophy.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M6 9H4.5a2.5 2.5 0 0 1 0-5H6', key: '17hqa7' }],\n  ['path', { d: 'M18 9h1.5a2.5 2.5 0 0 0 0-5H18', key: 'lmptdp' }],\n  ['path', { d: 'M4 22h16', key: '57wxv0' }],\n  ['path', { d: 'M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22', key: '1nw9bq' }],\n  ['path', { d: 'M14 14.66V17c0 .*********** 1.21C16.15 18.75 17 20.24 17 22', key: '1np0yb' }],\n  ['path', { d: 'M18 2H6v7a6 6 0 0 0 12 0V2Z', key: 'u46fv3' }],\n];\n\n/**\n * @component @name Trophy\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNiA5SDQuNWEyLjUgMi41IDAgMCAxIDAtNUg2IiAvPgogIDxwYXRoIGQ9Ik0xOCA5aDEuNWEyLjUgMi41IDAgMCAwIDAtNUgxOCIgLz4KICA8cGF0aCBkPSJNNCAyMmgxNiIgLz4KICA8cGF0aCBkPSJNMTAgMTQuNjZWMTdjMCAuNTUtLjQ3Ljk4LS45NyAxLjIxQzcuODUgMTguNzUgNyAyMC4yNCA3IDIyIiAvPgogIDxwYXRoIGQ9Ik0xNCAxNC42NlYxN2MwIC41NS40Ny45OC45NyAxLjIxQzE2LjE1IDE4Ljc1IDE3IDIwLjI0IDE3IDIyIiAvPgogIDxwYXRoIGQ9Ik0xOCAySDZ2N2E2IDYgMCAwIDAgMTIgMFYyWiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/trophy\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Trophy = createLucideIcon('trophy', __iconNode);\n\nexport default Trophy;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,8BAAgC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7D,CAAC,MAAQ;EAAED,CAAA,EAAG,gCAAkC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/D,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,4DAA8D;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3F,CAAC,MAAQ;EAAED,CAAA,EAAG,6DAA+D;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC5F,CAAC,MAAQ;EAAED,CAAA,EAAG,6BAA+B;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC9D;AAaM,MAAAC,MAAA,GAASC,gBAAiB,WAAUJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}