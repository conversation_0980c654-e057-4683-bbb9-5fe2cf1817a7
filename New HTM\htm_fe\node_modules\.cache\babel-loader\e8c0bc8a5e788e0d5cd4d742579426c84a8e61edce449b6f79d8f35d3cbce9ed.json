{"ast": null, "code": "var _s = $RefreshSig$();\n// Async operations hook\nimport { useState, useCallback, useRef, useEffect } from 'react';\nexport const useAsync = (asyncFunction, options = {}) => {\n  _s();\n  const {\n    immediate = false,\n    onSuccess,\n    onError\n  } = options;\n  const [state, setState] = useState({\n    data: null,\n    loading: false,\n    error: null\n  });\n  const mountedRef = useRef(true);\n  const lastCallId = useRef(0);\n  const execute = useCallback(async (...args) => {\n    const callId = ++lastCallId.current;\n    setState(prev => ({\n      ...prev,\n      loading: true,\n      error: null\n    }));\n    try {\n      const data = await asyncFunction(...args);\n\n      // Only update state if this is the most recent call and component is mounted\n      if (callId === lastCallId.current && mountedRef.current) {\n        setState({\n          data,\n          loading: false,\n          error: null\n        });\n        onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess(data);\n      }\n      return data;\n    } catch (error) {\n      // Only update state if this is the most recent call and component is mounted\n      if (callId === lastCallId.current && mountedRef.current) {\n        const errorMessage = (error === null || error === void 0 ? void 0 : error.message) || 'An error occurred';\n        setState(prev => ({\n          ...prev,\n          loading: false,\n          error: errorMessage\n        }));\n        onError === null || onError === void 0 ? void 0 : onError(error);\n      }\n      throw error;\n    }\n  }, [asyncFunction, onSuccess, onError]);\n  const reset = useCallback(() => {\n    setState({\n      data: null,\n      loading: false,\n      error: null\n    });\n  }, []);\n  const setData = useCallback(data => {\n    setState(prev => ({\n      ...prev,\n      data\n    }));\n  }, []);\n  const setError = useCallback(error => {\n    setState(prev => ({\n      ...prev,\n      error,\n      loading: false\n    }));\n  }, []);\n\n  // Execute immediately if requested\n  useEffect(() => {\n    if (immediate) {\n      execute();\n    }\n  }, [immediate, execute]);\n\n  // Cleanup on unmount\n  useEffect(() => {\n    return () => {\n      mountedRef.current = false;\n    };\n  }, []);\n  return {\n    ...state,\n    execute,\n    reset,\n    setData,\n    setError\n  };\n};\n_s(useAsync, \"1tZD0+mUMkYH8RxK9VLEwoRHpxU=\");\nexport default useAsync;", "map": {"version": 3, "names": ["useState", "useCallback", "useRef", "useEffect", "useAsync", "asyncFunction", "options", "_s", "immediate", "onSuccess", "onError", "state", "setState", "data", "loading", "error", "mountedRef", "lastCallId", "execute", "args", "callId", "current", "prev", "errorMessage", "message", "reset", "setData", "setError"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/shared/hooks/common/useAsync.ts"], "sourcesContent": ["// Async operations hook\r\nimport { useState, useCallback, useRef, useEffect } from 'react';\r\n\r\ninterface AsyncState<T> {\r\n  data: T | null;\r\n  loading: boolean;\r\n  error: string | null;\r\n}\r\n\r\ninterface UseAsyncOptions {\r\n  immediate?: boolean;\r\n  onSuccess?: (data: any) => void;\r\n  onError?: (error: any) => void;\r\n}\r\n\r\nexport const useAsync = <T = any>(\r\n  asyncFunction: (...args: any[]) => Promise<T>,\r\n  options: UseAsyncOptions = {}\r\n) => {\r\n  const { immediate = false, onSuccess, onError } = options;\r\n  \r\n  const [state, setState] = useState<AsyncState<T>>({\r\n    data: null,\r\n    loading: false,\r\n    error: null,\r\n  });\r\n\r\n  const mountedRef = useRef(true);\r\n  const lastCallId = useRef(0);\r\n\r\n  const execute = useCallback(\r\n    async (...args: any[]) => {\r\n      const callId = ++lastCallId.current;\r\n      \r\n      setState(prev => ({ ...prev, loading: true, error: null }));\r\n\r\n      try {\r\n        const data = await asyncFunction(...args);\r\n        \r\n        // Only update state if this is the most recent call and component is mounted\r\n        if (callId === lastCallId.current && mountedRef.current) {\r\n          setState({ data, loading: false, error: null });\r\n          onSuccess?.(data);\r\n        }\r\n        \r\n        return data;\r\n      } catch (error: any) {\r\n        // Only update state if this is the most recent call and component is mounted\r\n        if (callId === lastCallId.current && mountedRef.current) {\r\n          const errorMessage = error?.message || 'An error occurred';\r\n          setState(prev => ({ ...prev, loading: false, error: errorMessage }));\r\n          onError?.(error);\r\n        }\r\n        \r\n        throw error;\r\n      }\r\n    },\r\n    [asyncFunction, onSuccess, onError]\r\n  );\r\n\r\n  const reset = useCallback(() => {\r\n    setState({ data: null, loading: false, error: null });\r\n  }, []);\r\n\r\n  const setData = useCallback((data: T) => {\r\n    setState(prev => ({ ...prev, data }));\r\n  }, []);\r\n\r\n  const setError = useCallback((error: string) => {\r\n    setState(prev => ({ ...prev, error, loading: false }));\r\n  }, []);\r\n\r\n  // Execute immediately if requested\r\n  useEffect(() => {\r\n    if (immediate) {\r\n      execute();\r\n    }\r\n  }, [immediate, execute]);\r\n\r\n  // Cleanup on unmount\r\n  useEffect(() => {\r\n    return () => {\r\n      mountedRef.current = false;\r\n    };\r\n  }, []);\r\n\r\n  return {\r\n    ...state,\r\n    execute,\r\n    reset,\r\n    setData,\r\n    setError,\r\n  };\r\n};\r\n\r\nexport default useAsync;\r\n"], "mappings": ";AAAA;AACA,SAASA,QAAQ,EAAEC,WAAW,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAchE,OAAO,MAAMC,QAAQ,GAAGA,CACtBC,aAA6C,EAC7CC,OAAwB,GAAG,CAAC,CAAC,KAC1B;EAAAC,EAAA;EACH,MAAM;IAAEC,SAAS,GAAG,KAAK;IAAEC,SAAS;IAAEC;EAAQ,CAAC,GAAGJ,OAAO;EAEzD,MAAM,CAACK,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAgB;IAChDa,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,KAAK;IACdC,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,MAAMC,UAAU,GAAGd,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMe,UAAU,GAAGf,MAAM,CAAC,CAAC,CAAC;EAE5B,MAAMgB,OAAO,GAAGjB,WAAW,CACzB,OAAO,GAAGkB,IAAW,KAAK;IACxB,MAAMC,MAAM,GAAG,EAAEH,UAAU,CAACI,OAAO;IAEnCT,QAAQ,CAACU,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAER,OAAO,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC,CAAC;IAE3D,IAAI;MACF,MAAMF,IAAI,GAAG,MAAMR,aAAa,CAAC,GAAGc,IAAI,CAAC;;MAEzC;MACA,IAAIC,MAAM,KAAKH,UAAU,CAACI,OAAO,IAAIL,UAAU,CAACK,OAAO,EAAE;QACvDT,QAAQ,CAAC;UAAEC,IAAI;UAAEC,OAAO,EAAE,KAAK;UAAEC,KAAK,EAAE;QAAK,CAAC,CAAC;QAC/CN,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAGI,IAAI,CAAC;MACnB;MAEA,OAAOA,IAAI;IACb,CAAC,CAAC,OAAOE,KAAU,EAAE;MACnB;MACA,IAAIK,MAAM,KAAKH,UAAU,CAACI,OAAO,IAAIL,UAAU,CAACK,OAAO,EAAE;QACvD,MAAME,YAAY,GAAG,CAAAR,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAES,OAAO,KAAI,mBAAmB;QAC1DZ,QAAQ,CAACU,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAER,OAAO,EAAE,KAAK;UAAEC,KAAK,EAAEQ;QAAa,CAAC,CAAC,CAAC;QACpEb,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAGK,KAAK,CAAC;MAClB;MAEA,MAAMA,KAAK;IACb;EACF,CAAC,EACD,CAACV,aAAa,EAAEI,SAAS,EAAEC,OAAO,CACpC,CAAC;EAED,MAAMe,KAAK,GAAGxB,WAAW,CAAC,MAAM;IAC9BW,QAAQ,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC;EACvD,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMW,OAAO,GAAGzB,WAAW,CAAEY,IAAO,IAAK;IACvCD,QAAQ,CAACU,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAET;IAAK,CAAC,CAAC,CAAC;EACvC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMc,QAAQ,GAAG1B,WAAW,CAAEc,KAAa,IAAK;IAC9CH,QAAQ,CAACU,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEP,KAAK;MAAED,OAAO,EAAE;IAAM,CAAC,CAAC,CAAC;EACxD,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAX,SAAS,CAAC,MAAM;IACd,IAAIK,SAAS,EAAE;MACbU,OAAO,CAAC,CAAC;IACX;EACF,CAAC,EAAE,CAACV,SAAS,EAAEU,OAAO,CAAC,CAAC;;EAExB;EACAf,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXa,UAAU,CAACK,OAAO,GAAG,KAAK;IAC5B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO;IACL,GAAGV,KAAK;IACRO,OAAO;IACPO,KAAK;IACLC,OAAO;IACPC;EACF,CAAC;AACH,CAAC;AAACpB,EAAA,CA9EWH,QAAQ;AAgFrB,eAAeA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}