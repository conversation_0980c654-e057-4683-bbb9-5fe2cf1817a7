{"ast": null, "code": "'use strict';\n\nvar fails = require('../internals/fails');\nmodule.exports = !fails(function () {\n  // eslint-disable-next-line es/no-function-prototype-bind -- safe\n  var test = function () {/* empty */}.bind();\n  // eslint-disable-next-line no-prototype-builtins -- safe\n  return typeof test != 'function' || test.hasOwnProperty('prototype');\n});", "map": {"version": 3, "names": ["fails", "require", "module", "exports", "test", "bind", "hasOwnProperty"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/node_modules/core-js-pure/internals/function-bind-native.js"], "sourcesContent": ["'use strict';\nvar fails = require('../internals/fails');\n\nmodule.exports = !fails(function () {\n  // eslint-disable-next-line es/no-function-prototype-bind -- safe\n  var test = (function () { /* empty */ }).bind();\n  // eslint-disable-next-line no-prototype-builtins -- safe\n  return typeof test != 'function' || test.hasOwnProperty('prototype');\n});\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,oBAAoB,CAAC;AAEzCC,MAAM,CAACC,OAAO,GAAG,CAACH,KAAK,CAAC,YAAY;EAClC;EACA,IAAII,IAAI,GAAI,YAAY,CAAE,YAAa,CAAEC,IAAI,CAAC,CAAC;EAC/C;EACA,OAAO,OAAOD,IAAI,IAAI,UAAU,IAAIA,IAAI,CAACE,cAAc,CAAC,WAAW,CAAC;AACtE,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}