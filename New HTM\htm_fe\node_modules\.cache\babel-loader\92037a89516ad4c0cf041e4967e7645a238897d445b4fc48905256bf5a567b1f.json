{"ast": null, "code": "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 5H6a2 2 0 0 0-2 2v3\",\n  key: \"l96uqu\"\n}], [\"path\", {\n  d: \"m9 8 3-3-3-3\",\n  key: \"1gzgc3\"\n}], [\"path\", {\n  d: \"M4 14v4a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7a2 2 0 0 0-2-2h-2\",\n  key: \"1w2k5h\"\n}]];\nconst RotateCwSquare = createLucideIcon(\"rotate-cw-square\", __iconNode);\nexport { __iconNode, RotateCwSquare as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "RotateCwSquare", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\node_modules\\lucide-react\\src\\icons\\rotate-cw-square.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 5H6a2 2 0 0 0-2 2v3', key: 'l96uqu' }],\n  ['path', { d: 'm9 8 3-3-3-3', key: '1gzgc3' }],\n  ['path', { d: 'M4 14v4a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7a2 2 0 0 0-2-2h-2', key: '1w2k5h' }],\n];\n\n/**\n * @component @name RotateCwSquare\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgNUg2YTIgMiAwIDAgMC0yIDJ2MyIgLz4KICA8cGF0aCBkPSJtOSA4IDMtMy0zLTMiIC8+CiAgPHBhdGggZD0iTTQgMTR2NGEyIDIgMCAwIDAgMiAyaDEyYTIgMiAwIDAgMCAyLTJWN2EyIDIgMCAwIDAtMi0yaC0yIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/rotate-cw-square\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst RotateCwSquare = createLucideIcon('rotate-cw-square', __iconNode);\n\nexport default RotateCwSquare;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,yBAA2B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,cAAgB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7C,CAAC,MAAQ;EAAED,CAAA,EAAG,2DAA6D;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC5F;AAaM,MAAAC,cAAA,GAAiBC,gBAAiB,qBAAoBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}