{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\New HTM\\\\htm_fe\\\\src\\\\shared\\\\components\\\\ui\\\\Input\\\\Input.tsx\";\n// Input component\nimport React, { forwardRef } from 'react';\nimport { getInputClasses, labelClasses, errorClasses, helperTextClasses } from './Input.variants';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const Input = /*#__PURE__*/forwardRef(_c = ({\n  label,\n  error,\n  helperText,\n  variant = 'default',\n  size = 'md',\n  leftIcon,\n  rightIcon,\n  isRequired = false,\n  isDisabled = false,\n  fullWidth = true,\n  className,\n  containerClassName,\n  id,\n  ...props\n}, ref) => {\n  const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;\n  const hasError = !!error;\n  const finalVariant = hasError ? 'error' : variant;\n  const inputClasses = getInputClasses(finalVariant, size, !!leftIcon, !!rightIcon, className);\n  const containerClasses = [fullWidth ? 'w-full' : '', containerClassName || ''].filter(Boolean).join(' ');\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: containerClasses,\n    children: [label && /*#__PURE__*/_jsxDEV(\"label\", {\n      htmlFor: inputId,\n      className: labelClasses,\n      children: [label, isRequired && /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-red-500 ml-1\",\n        children: \"*\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 28\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 11\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative\",\n      children: [leftIcon && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-400\",\n          children: leftIcon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        ref: ref,\n        id: inputId,\n        className: inputClasses,\n        disabled: isDisabled,\n        required: isRequired,\n        ...props\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 11\n      }, this), rightIcon && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-400\",\n          children: rightIcon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 9\n    }, this), error && /*#__PURE__*/_jsxDEV(\"p\", {\n      className: errorClasses,\n      role: \"alert\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 11\n    }, this), helperText && !error && /*#__PURE__*/_jsxDEV(\"p\", {\n      className: helperTextClasses,\n      children: helperText\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 11\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 7\n  }, this);\n});\n_c2 = Input;\nInput.displayName = 'Input';\nexport default Input;\nvar _c, _c2;\n$RefreshReg$(_c, \"Input$forwardRef\");\n$RefreshReg$(_c2, \"Input\");", "map": {"version": 3, "names": ["React", "forwardRef", "getInputClasses", "labelClasses", "errorClasses", "helperTextClasses", "jsxDEV", "_jsxDEV", "Input", "_c", "label", "error", "helperText", "variant", "size", "leftIcon", "rightIcon", "isRequired", "isDisabled", "fullWidth", "className", "containerClassName", "id", "props", "ref", "inputId", "Math", "random", "toString", "substr", "<PERSON><PERSON><PERSON><PERSON>", "final<PERSON><PERSON><PERSON>", "inputClasses", "containerClasses", "filter", "Boolean", "join", "children", "htmlFor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "disabled", "required", "role", "_c2", "displayName", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/shared/components/ui/Input/Input.tsx"], "sourcesContent": ["// Input component\r\nimport React, { forwardRef } from 'react';\r\nimport { InputProps } from './Input.types';\r\nimport { getInputClasses, labelClasses, errorClasses, helperTextClasses } from './Input.variants';\r\n\r\nexport const Input = forwardRef<HTMLInputElement, InputProps>(\r\n  (\r\n    {\r\n      label,\r\n      error,\r\n      helperText,\r\n      variant = 'default',\r\n      size = 'md',\r\n      leftIcon,\r\n      rightIcon,\r\n      isRequired = false,\r\n      isDisabled = false,\r\n      fullWidth = true,\r\n      className,\r\n      containerClassName,\r\n      id,\r\n      ...props\r\n    },\r\n    ref\r\n  ) => {\r\n    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;\r\n    const hasError = !!error;\r\n    const finalVariant = hasError ? 'error' : variant;\r\n    \r\n    const inputClasses = getInputClasses(\r\n      finalVariant,\r\n      size,\r\n      !!leftIcon,\r\n      !!rightIcon,\r\n      className\r\n    );\r\n\r\n    const containerClasses = [\r\n      fullWidth ? 'w-full' : '',\r\n      containerClassName || '',\r\n    ].filter(Boolean).join(' ');\r\n\r\n    return (\r\n      <div className={containerClasses}>\r\n        {label && (\r\n          <label htmlFor={inputId} className={labelClasses}>\r\n            {label}\r\n            {isRequired && <span className=\"text-red-500 ml-1\">*</span>}\r\n          </label>\r\n        )}\r\n        \r\n        <div className=\"relative\">\r\n          {leftIcon && (\r\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n              <span className=\"text-gray-400\">{leftIcon}</span>\r\n            </div>\r\n          )}\r\n          \r\n          <input\r\n            ref={ref}\r\n            id={inputId}\r\n            className={inputClasses}\r\n            disabled={isDisabled}\r\n            required={isRequired}\r\n            {...props}\r\n          />\r\n          \r\n          {rightIcon && (\r\n            <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\">\r\n              <span className=\"text-gray-400\">{rightIcon}</span>\r\n            </div>\r\n          )}\r\n        </div>\r\n        \r\n        {error && (\r\n          <p className={errorClasses} role=\"alert\">\r\n            {error}\r\n          </p>\r\n        )}\r\n        \r\n        {helperText && !error && (\r\n          <p className={helperTextClasses}>\r\n            {helperText}\r\n          </p>\r\n        )}\r\n      </div>\r\n    );\r\n  }\r\n);\r\n\r\nInput.displayName = 'Input';\r\n\r\nexport default Input;\r\n"], "mappings": ";AAAA;AACA,OAAOA,KAAK,IAAIC,UAAU,QAAQ,OAAO;AAEzC,SAASC,eAAe,EAAEC,YAAY,EAAEC,YAAY,EAAEC,iBAAiB,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElG,OAAO,MAAMC,KAAK,gBAAGP,UAAU,CAAAQ,EAAA,GAC7BA,CACE;EACEC,KAAK;EACLC,KAAK;EACLC,UAAU;EACVC,OAAO,GAAG,SAAS;EACnBC,IAAI,GAAG,IAAI;EACXC,QAAQ;EACRC,SAAS;EACTC,UAAU,GAAG,KAAK;EAClBC,UAAU,GAAG,KAAK;EAClBC,SAAS,GAAG,IAAI;EAChBC,SAAS;EACTC,kBAAkB;EAClBC,EAAE;EACF,GAAGC;AACL,CAAC,EACDC,GAAG,KACA;EACH,MAAMC,OAAO,GAAGH,EAAE,IAAI,SAASI,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;EACxE,MAAMC,QAAQ,GAAG,CAAC,CAACnB,KAAK;EACxB,MAAMoB,YAAY,GAAGD,QAAQ,GAAG,OAAO,GAAGjB,OAAO;EAEjD,MAAMmB,YAAY,GAAG9B,eAAe,CAClC6B,YAAY,EACZjB,IAAI,EACJ,CAAC,CAACC,QAAQ,EACV,CAAC,CAACC,SAAS,EACXI,SACF,CAAC;EAED,MAAMa,gBAAgB,GAAG,CACvBd,SAAS,GAAG,QAAQ,GAAG,EAAE,EACzBE,kBAAkB,IAAI,EAAE,CACzB,CAACa,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;EAE3B,oBACE7B,OAAA;IAAKa,SAAS,EAAEa,gBAAiB;IAAAI,QAAA,GAC9B3B,KAAK,iBACJH,OAAA;MAAO+B,OAAO,EAAEb,OAAQ;MAACL,SAAS,EAAEjB,YAAa;MAAAkC,QAAA,GAC9C3B,KAAK,EACLO,UAAU,iBAAIV,OAAA;QAAMa,SAAS,EAAC,mBAAmB;QAAAiB,QAAA,EAAC;MAAC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtD,CACR,eAEDnC,OAAA;MAAKa,SAAS,EAAC,UAAU;MAAAiB,QAAA,GACtBtB,QAAQ,iBACPR,OAAA;QAAKa,SAAS,EAAC,sEAAsE;QAAAiB,QAAA,eACnF9B,OAAA;UAAMa,SAAS,EAAC,eAAe;UAAAiB,QAAA,EAAEtB;QAAQ;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CACN,eAEDnC,OAAA;QACEiB,GAAG,EAAEA,GAAI;QACTF,EAAE,EAAEG,OAAQ;QACZL,SAAS,EAAEY,YAAa;QACxBW,QAAQ,EAAEzB,UAAW;QACrB0B,QAAQ,EAAE3B,UAAW;QAAA,GACjBM;MAAK;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,EAED1B,SAAS,iBACRT,OAAA;QAAKa,SAAS,EAAC,uEAAuE;QAAAiB,QAAA,eACpF9B,OAAA;UAAMa,SAAS,EAAC,eAAe;UAAAiB,QAAA,EAAErB;QAAS;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAEL/B,KAAK,iBACJJ,OAAA;MAAGa,SAAS,EAAEhB,YAAa;MAACyC,IAAI,EAAC,OAAO;MAAAR,QAAA,EACrC1B;IAAK;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACJ,EAEA9B,UAAU,IAAI,CAACD,KAAK,iBACnBJ,OAAA;MAAGa,SAAS,EAAEf,iBAAkB;MAAAgC,QAAA,EAC7BzB;IAAU;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACJ;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CACF,CAAC;AAACI,GAAA,GAnFWtC,KAAK;AAqFlBA,KAAK,CAACuC,WAAW,GAAG,OAAO;AAE3B,eAAevC,KAAK;AAAC,IAAAC,EAAA,EAAAqC,GAAA;AAAAE,YAAA,CAAAvC,EAAA;AAAAuC,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}