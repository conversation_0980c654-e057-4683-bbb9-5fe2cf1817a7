// MIGRATED VERSION: Round1 using Redux and new hooks
import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useSearchParams } from 'react-router-dom';
import { useAppSelector, useAppDispatch } from '../../app/store';
import { setCurrentQuestion, nextQuestion, setPlayers } from '../../app/store/slices/gameSlice';
import { addToast } from '../../app/store/slices/uiSlice';
import { useGameApi, useFirebaseListener } from '../../shared/hooks';
import { Button, Input } from '../../shared/components/ui';
import { GameState, AuthState } from '../../shared/types';
import { Question } from '../../type';
import { listenToTimeStart, listenToAnswers, listenToSound, deletePath } from '../../services/firebaseServices';
import { submitAnswer } from '../services';
import PlayerAnswerInput from '../../components/ui/PlayerAnswerInput';
import { useTimeStart } from '../../context/timeListenerContext';
import { useSounds } from '../../context/soundContext';

interface Round1Props {
  isHost?: boolean;
  isSpectator?: boolean;
}

const Round1: React.FC<Round1Props> = ({ isHost = false, isSpectator = false }) => {
  const sounds = useSounds();
  const [searchParams] = useSearchParams();
  const dispatch = useAppDispatch();

  // Redux state
  const {
    currentQuestion,
    questions,
    questionNumber,
    players,
    loading
  } = useAppSelector((state) => state.game as GameState);

  const { user } = useAppSelector((state) => state.auth as AuthState);

  // URL params
  const roomId = searchParams.get("roomId") || "";
  const testName = searchParams.get("testName") || "";

  // Local state (matching original functionality)
  const [correctAnswer, setCorrectAnswer] = useState<string>("");
  const [isExpanded, setIsExpanded] = useState(false);
  const [isInputDisabled, setIsInputDisabled] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Timer functionality (using existing context for compatibility)
  const { timeLeft, playerAnswerTime, startTimer } = useTimeStart();

  // Player data from localStorage (for backward compatibility)
  const position = localStorage.getItem("position") || "";
  const currentPlayerName = localStorage.getItem("currentPlayerName") || "";
  const currentPlayerAvatar = localStorage.getItem("currentPlayerAvatar") || "";
  const playerAnswerRef = useRef<string>("");

  // Initialize refs for timer logic (matching original)
  const isInitialMount = useRef(false);
  const isInitialTimerMount = useRef(true);
  
  // Hooks
  const { 
    getQuestions, 
    submitPlayerAnswer
  } = useGameApi();
  
  const { 
    listenToCurrentQuestion,
    setCurrentQuestionFirebase 
  } = useFirebaseListener(roomId);

  // Helper function to update answer list in Redux
  const setAnswerList = (answers: any[]) => {
    const updatedPlayers = players.map(player => ({
      ...player,
      answer: answers.find(a => a.uid === player.uid)?.answer || player.answer
    }));
    dispatch(setPlayers(updatedPlayers));
  };

  // Timer start listener (matching original logic)
  useEffect(() => {
    const unsubscribe = listenToTimeStart(roomId, async () => {
      if (isInitialMount.current) {
        isInitialMount.current = false;
        return;
      }
      startTimer(10);
      setIsInputDisabled(false);
    });

    return () => {
      unsubscribe();
    };
  }, [roomId, startTimer]);

  // Timer countdown logic (matching original)
  useEffect(() => {
    console.log("timeLeft", timeLeft);

    if (isInitialTimerMount.current) {
      isInitialTimerMount.current = false;
      return;
    }

    if (timeLeft === 0) {
      setIsInputDisabled(true);

      if (!isHost && !isSpectator) {
        console.log("playerAnswerRef.current", playerAnswerRef.current);
        console.log("position", position);

        // When timer runs out, submit answer
        submitAnswer(roomId, playerAnswerRef.current, position, playerAnswerTime, currentPlayerName, currentPlayerAvatar);
      }
    }
  }, [timeLeft, isHost, isSpectator, roomId, position, playerAnswerTime, currentPlayerName, currentPlayerAvatar]);

  // Listen to current question changes (using Redux hook)
  useEffect(() => {
    if (!roomId) return;

    return listenToCurrentQuestion((question) => {
      if (question) {
        console.log("current question", question);
        setAnswerList([]);

        if (!isHost) {
          setCorrectAnswer("");
        }
      }
    });
  }, [roomId, listenToCurrentQuestion, isHost]);

  // Listen to sound effects (matching original)
  useEffect(() => {
    const unsubscribePlayers = listenToSound(roomId, async (type: string) => {
      const audio = sounds[`${type}`];
      if (audio) {
        audio.play();
      }
      console.log("sound type", type);
      await deletePath(roomId, "sound");
    });

    return () => {
      unsubscribePlayers();
    };
  }, [roomId, sounds]);

  // Listen to answers (matching original)
  useEffect(() => {
    const unsubscribePlayers = listenToAnswers(roomId, (answer: string) => {
      const audio = sounds['correct'];
      if (audio) {
        audio.play();
      }
      setCorrectAnswer(`Đáp án: ${answer}`);
    });

    return () => {
      unsubscribePlayers();
    };
  }, [roomId, sounds]);

  // Render the question component (matching original UI)
  return (
    <div
      className={`bg-slate-800/80 backdrop-blur-sm rounded-xl border border-blue-400/30 shadow-2xl p-6 mb-4 w-full flex flex-col items-center`}
    >
      {/* Question text */}
      <div
        className={`text-white text-xl font-semibold text-center mb-4 max-w-[90%] ${isExpanded ? "max-h-none" : "max-h-[120px] overflow-hidden"
          }`}
      >
        {currentQuestion?.question}
      </div>

      {/* Correct answer */}
      <div
        className={`text-cyan-200 text-lg font-semibold text-center mb-4 max-w-[90%] ${isExpanded ? "max-h-none" : "max-h-[120px] overflow-hidden"
          }`}
      >
        {correctAnswer}
      </div>

      {/* Media */}
      <div
        className={`w-full h-[300px] flex items-center justify-center overflow-hidden cursor-pointer mb-4 min-h-[400px]`}
        onClick={() => setIsModalOpen(true)}
      >
        {(() => {
          const url = currentQuestion?.imgUrl;
          if (!url) return <p className="text-white">No media</p>;

          const extension = url.split('.').pop()?.toLowerCase() || "";

          if (["jpg", "jpeg", "png", "gif", "webp"].includes(extension)) {
            return <img src={url} alt="Question Visual" className="w-full h-full object-cover rounded-lg" />;
          }

          if (["mp3", "wav", "ogg"].includes(extension)) {
            return <audio controls className="w-full h-full">
              <source src={url} type={`audio/${extension}`} />
              Your browser does not support the audio element.
            </audio>;
          }

          if (["mp4", "webm", "ogg"].includes(extension)) {
            return <video controls className="w-full h-full object-cover rounded-lg">
              <source src={url} type={`video/${extension}`} />
              Your browser does not support the video tag.
            </video>;
          }

          return <p className="text-white">Unsupported media type</p>;
        })()}
      </div>

      {/* Answer input */}
      {!isSpectator && (
        <PlayerAnswerInput
          isHost={isHost}
          question={currentQuestion}
          isDisabled={isInputDisabled}
          playerAnswerRef={playerAnswerRef}
        />
      )}

      {/* Modal for full-size image */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-80 flex justify-center items-center z-50"
          onClick={() => setIsModalOpen(false)}>
          <img src={currentQuestion?.imgUrl} alt="Full Size" className="max-w-full max-h-full rounded-xl" />
        </div>
      )}
    </div>
  );
};

export default Round1;
