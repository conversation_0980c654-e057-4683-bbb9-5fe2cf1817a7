{"ast": null, "code": "// Input variant styles using Tailwind CSS\n\nexport const inputVariants = {\n  default: 'border-gray-300 focus:ring-blue-500 focus:border-blue-500',\n  error: 'border-red-300 focus:ring-red-500 focus:border-red-500',\n  success: 'border-green-300 focus:ring-green-500 focus:border-green-500'\n};\nexport const inputSizes = {\n  sm: 'px-3 py-2 text-sm',\n  md: 'px-4 py-2 text-base',\n  lg: 'px-4 py-3 text-lg'\n};\nexport const baseInputClasses = 'block w-full rounded-md border shadow-sm focus:outline-none focus:ring-1 disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed transition-colors duration-200';\nexport const labelClasses = 'block text-sm font-medium text-gray-700 mb-1';\nexport const errorClasses = 'mt-1 text-sm text-red-600';\nexport const helperTextClasses = 'mt-1 text-sm text-gray-500';\nexport const getInputClasses = (variant, size, hasLeftIcon, hasRightIcon, className) => {\n  const classes = [baseInputClasses, inputVariants[variant], inputSizes[size], hasLeftIcon ? 'pl-10' : '', hasRightIcon ? 'pr-10' : '', className || ''];\n  return classes.filter(Boolean).join(' ');\n};", "map": {"version": 3, "names": ["inputVariants", "default", "error", "success", "inputSizes", "sm", "md", "lg", "baseInputClasses", "labelClasses", "errorClasses", "helperTextClasses", "getInputClasses", "variant", "size", "hasLeftIcon", "hasRightIcon", "className", "classes", "filter", "Boolean", "join"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/shared/components/ui/Input/Input.variants.ts"], "sourcesContent": ["// Input variant styles using Tailwind CSS\r\nimport { InputVariant, InputSize } from './Input.types';\r\n\r\nexport const inputVariants: Record<InputVariant, string> = {\r\n  default: 'border-gray-300 focus:ring-blue-500 focus:border-blue-500',\r\n  error: 'border-red-300 focus:ring-red-500 focus:border-red-500',\r\n  success: 'border-green-300 focus:ring-green-500 focus:border-green-500',\r\n};\r\n\r\nexport const inputSizes: Record<InputSize, string> = {\r\n  sm: 'px-3 py-2 text-sm',\r\n  md: 'px-4 py-2 text-base',\r\n  lg: 'px-4 py-3 text-lg',\r\n};\r\n\r\nexport const baseInputClasses = 'block w-full rounded-md border shadow-sm focus:outline-none focus:ring-1 disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed transition-colors duration-200';\r\n\r\nexport const labelClasses = 'block text-sm font-medium text-gray-700 mb-1';\r\n\r\nexport const errorClasses = 'mt-1 text-sm text-red-600';\r\n\r\nexport const helperTextClasses = 'mt-1 text-sm text-gray-500';\r\n\r\nexport const getInputClasses = (\r\n  variant: InputVariant,\r\n  size: InputSize,\r\n  hasLeftIcon: boolean,\r\n  hasRightIcon: boolean,\r\n  className?: string\r\n): string => {\r\n  const classes = [\r\n    baseInputClasses,\r\n    inputVariants[variant],\r\n    inputSizes[size],\r\n    hasLeftIcon ? 'pl-10' : '',\r\n    hasRightIcon ? 'pr-10' : '',\r\n    className || '',\r\n  ];\r\n\r\n  return classes.filter(Boolean).join(' ');\r\n};\r\n"], "mappings": "AAAA;;AAGA,OAAO,MAAMA,aAA2C,GAAG;EACzDC,OAAO,EAAE,2DAA2D;EACpEC,KAAK,EAAE,wDAAwD;EAC/DC,OAAO,EAAE;AACX,CAAC;AAED,OAAO,MAAMC,UAAqC,GAAG;EACnDC,EAAE,EAAE,mBAAmB;EACvBC,EAAE,EAAE,qBAAqB;EACzBC,EAAE,EAAE;AACN,CAAC;AAED,OAAO,MAAMC,gBAAgB,GAAG,gLAAgL;AAEhN,OAAO,MAAMC,YAAY,GAAG,8CAA8C;AAE1E,OAAO,MAAMC,YAAY,GAAG,2BAA2B;AAEvD,OAAO,MAAMC,iBAAiB,GAAG,4BAA4B;AAE7D,OAAO,MAAMC,eAAe,GAAGA,CAC7BC,OAAqB,EACrBC,IAAe,EACfC,WAAoB,EACpBC,YAAqB,EACrBC,SAAkB,KACP;EACX,MAAMC,OAAO,GAAG,CACdV,gBAAgB,EAChBR,aAAa,CAACa,OAAO,CAAC,EACtBT,UAAU,CAACU,IAAI,CAAC,EAChBC,WAAW,GAAG,OAAO,GAAG,EAAE,EAC1BC,YAAY,GAAG,OAAO,GAAG,EAAE,EAC3BC,SAAS,IAAI,EAAE,CAChB;EAED,OAAOC,OAAO,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;AAC1C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}