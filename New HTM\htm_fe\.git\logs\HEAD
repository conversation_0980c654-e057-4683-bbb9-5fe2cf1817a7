f637729273d62ee1fe6108d1845a8f843643e23f f637729273d62ee1fe6108d1845a8f843643e23f Baonguyen1711 <<EMAIL>> 1748333286 +0700	checkout: moving from main to main
f637729273d62ee1fe6108d1845a8f843643e23f 0738d2eb29e1032843da547b649f6f53cd4663e9 Baonguyen1711 <<EMAIL>> 1748333309 +0700	commit: finish all functions
0738d2eb29e1032843da547b649f6f53cd4663e9 666d6ea9a64abfdd35f596fad5650bddc4274859 Baonguyen1711 <<EMAIL>> 1748339394 +0700	commit: modify style
666d6ea9a64abfdd35f596fad5650bddc4274859 d61d1b4bb981f2eaf00d34664763634592811092 Baonguyen1711 <<EMAIL>> 1748447755 +0700	commit: remake UI
d61d1b4bb981f2eaf00d34664763634592811092 a0ac6e9da5f25cae598d7707a1c5ca2e837533f9 Baonguyen1711 <<EMAIL>> 1748537710 +0700	commit: add spectator view
a0ac6e9da5f25cae598d7707a1c5ca2e837533f9 f9560fffeeadaf2ef6e1ea8ce25e747ab2553fed Baonguyen1711 <<EMAIL>> 1748605161 +0700	commit: scale UI and add restore game state
f9560fffeeadaf2ef6e1ea8ce25e747ab2553fed d7c97ebc7da4ac8d9d8819c76da52e2ee324b8cb Baonguyen1711 <<EMAIL>> 1748879666 +0700	commit: add nshv and scale UI down
d7c97ebc7da4ac8d9d8819c76da52e2ee324b8cb 3f564d5ae8d2281f3bf1dd9d3c36bc98176485e0 Baonguyen1711 <<EMAIL>> 1751202897 +0700	commit: finish all features before refactoring
3f564d5ae8d2281f3bf1dd9d3c36bc98176485e0 2758461afa5571980bc7fb7ca4d93e4287785773 Baonguyen1711 <<EMAIL>> 1751204764 +0700	commit: add password for room
2758461afa5571980bc7fb7ca4d93e4287785773 a0687f0140127681587400d31d54711572d77976 Baonguyen1711 <<EMAIL>> 1751206476 +0700	commit: add maximum player for room
a0687f0140127681587400d31d54711572d77976 7d1f3113566b0e574a47d9c567af2c7398844676 Baonguyen1711 <<EMAIL>> 1751213981 +0700	commit: add display answer and prefetch question for host side
7d1f3113566b0e574a47d9c567af2c7398844676 eb5d639ba9fe1f98e390d69a5f94b1445e03914d Baonguyen1711 <<EMAIL>> 1751368917 +0700	commit: add rules and host instruction
eb5d639ba9fe1f98e390d69a5f94b1445e03914d f5105b3a6f6dc57feebb1a55813855fd6e23c989 Baonguyen1711 <<EMAIL>> 1751448079 +0700	commit: add custom diffulty for round 4
f5105b3a6f6dc57feebb1a55813855fd6e23c989 b543ebe06b40644d9596b12553809b57de6b79e8 Baonguyen1711 <<EMAIL>> 1751471380 +0700	commit: fix cookies bug and add refresh token
b543ebe06b40644d9596b12553809b57de6b79e8 b543ebe06b40644d9596b12553809b57de6b79e8 Baonguyen1711 <<EMAIL>> 1751515887 +0700	reset: moving to HEAD
b543ebe06b40644d9596b12553809b57de6b79e8 20ed4a02a994365859ac251964964bace052ef63 Baonguyen1711 <<EMAIL>> 1751714588 +0700	commit: add used topics logic and improve UI for round 3
20ed4a02a994365859ac251964964bace052ef63 5500cfd8dc4307b987efa7d0538833e1ea528a66 Baonguyen1711 <<EMAIL>> 1751716955 +0700	commit: add: - Lock player input before time start. - Alert when maximum question exceed. - Fix host button and guide
5500cfd8dc4307b987efa7d0538833e1ea528a66 787578fe997160d271aa1fd6f60b8b15e9dba218 Baonguyen1711 <<EMAIL>> 1751784313 +0700	commit: add logout and fix round 4 question
787578fe997160d271aa1fd6f60b8b15e9dba218 37b0a5cf47a8f0d1b01c3f7bf644afed57433dcc Baonguyen1711 <<EMAIL>> 1751813706 +0700	commit: add error boundary and grid recovery for fallback UI
37b0a5cf47a8f0d1b01c3f7bf644afed57433dcc 37b0a5cf47a8f0d1b01c3f7bf644afed57433dcc Baonguyen1711 <<EMAIL>> 1753324245 +0700	checkout: moving from main to refactor
37b0a5cf47a8f0d1b01c3f7bf644afed57433dcc 0ff466aa179afe39c8f5cf4b7b2fa7f08d857847 Baonguyen1711 <<EMAIL>> 1753324270 +0700	commit: refactor code
0ff466aa179afe39c8f5cf4b7b2fa7f08d857847 37b0a5cf47a8f0d1b01c3f7bf644afed57433dcc Baonguyen1711 <<EMAIL>> 1753324364 +0700	checkout: moving from refactor to main
37b0a5cf47a8f0d1b01c3f7bf644afed57433dcc 0ff466aa179afe39c8f5cf4b7b2fa7f08d857847 Baonguyen1711 <<EMAIL>> 1753345975 +0700	checkout: moving from main to refactor
0ff466aa179afe39c8f5cf4b7b2fa7f08d857847 37b0a5cf47a8f0d1b01c3f7bf644afed57433dcc Baonguyen1711 <<EMAIL>> 1753349227 +0700	checkout: moving from refactor to main
37b0a5cf47a8f0d1b01c3f7bf644afed57433dcc 0ff466aa179afe39c8f5cf4b7b2fa7f08d857847 Baonguyen1711 <<EMAIL>> 1753352500 +0700	checkout: moving from main to refactor
