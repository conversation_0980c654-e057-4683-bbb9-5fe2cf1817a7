{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\New HTM\\\\htm_fe\\\\src\\\\components\\\\PlayerColorSelector.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { XMarkIcon, PaintBrushIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AVAILABLE_COLORS = [{\n  name: 'Đỏ',\n  value: '#FF0000',\n  bg: 'bg-red-500'\n}, {\n  name: 'Xanh lá',\n  value: '#00FF00',\n  bg: 'bg-green-500'\n}, {\n  name: '<PERSON>anh dương',\n  value: '#0000FF',\n  bg: 'bg-blue-500'\n}, {\n  name: 'Vàng',\n  value: '#FFFF00',\n  bg: 'bg-yellow-500'\n}, {\n  name: 'Tím',\n  value: '#800080',\n  bg: 'bg-purple-500'\n}, {\n  name: 'Cam',\n  value: '#FFA500',\n  bg: 'bg-orange-500'\n}, {\n  name: 'Hồng',\n  value: '#FF69B4',\n  bg: 'bg-pink-500'\n}, {\n  name: 'Xanh lam',\n  value: '#00FFFF',\n  bg: 'bg-cyan-500'\n}];\nconst PlayerColorSelector = ({\n  isOpen,\n  onClose,\n  players,\n  onSaveColors,\n  currentColors = {}\n}) => {\n  _s();\n  const [selectedColors, setSelectedColors] = useState(currentColors);\n  const [usedColors, setUsedColors] = useState(new Set());\n  useEffect(() => {\n    setSelectedColors(currentColors);\n    setUsedColors(new Set(Object.values(currentColors)));\n  }, [currentColors, isOpen]);\n  if (!isOpen) return null;\n  const handleColorSelect = (playerStt, color) => {\n    const newSelectedColors = {\n      ...selectedColors\n    };\n    const newUsedColors = new Set(usedColors);\n\n    // Remove old color if exists\n    if (selectedColors[playerStt]) {\n      newUsedColors.delete(selectedColors[playerStt]);\n    }\n\n    // Add new color\n    newSelectedColors[playerStt] = color;\n    newUsedColors.add(color);\n    setSelectedColors(newSelectedColors);\n    setUsedColors(newUsedColors);\n  };\n  const handleRemoveColor = playerStt => {\n    const newSelectedColors = {\n      ...selectedColors\n    };\n    const newUsedColors = new Set(usedColors);\n    if (selectedColors[playerStt]) {\n      newUsedColors.delete(selectedColors[playerStt]);\n      delete newSelectedColors[playerStt];\n    }\n    setSelectedColors(newSelectedColors);\n    setUsedColors(newUsedColors);\n  };\n  const handleSave = () => {\n    onSaveColors(selectedColors);\n    onClose();\n  };\n  const getColorName = colorValue => {\n    const color = AVAILABLE_COLORS.find(c => c.value === colorValue);\n    return color ? color.name : colorValue;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 z-50 flex items-center justify-center\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 bg-black/70 backdrop-blur-sm\",\n      onClick: onClose\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl shadow-2xl border border-purple-400/30 max-w-5xl w-full mx-4 max-h-[90vh] overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-purple-600 to-pink-600 px-4 py-3 flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(PaintBrushIcon, {\n            className: \"w-6 h-6 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-lg font-bold text-white\",\n            children: \"Ch\\u1ECDn m\\xE0u cho th\\xED sinh - V\\xF2ng 4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onClose,\n          className: \"text-white hover:text-gray-300 transition-colors p-1 rounded-lg hover:bg-white/10\",\n          children: /*#__PURE__*/_jsxDEV(XMarkIcon, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 overflow-y-auto max-h-[75vh]\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 lg:grid-cols-2 gap-4\",\n          children: players.map((player, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-slate-700/50 rounded-lg p-4 border border-slate-600/50\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: player.avatar || '/default-avatar.png',\n                  alt: player.userName,\n                  className: \"w-10 h-10 rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 116,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-white font-semibold\",\n                    children: player.userName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 122,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-400 text-sm\",\n                    children: [\"Th\\xED sinh \", player.stt]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 123,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 121,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 115,\n                columnNumber: 19\n              }, this), selectedColors[player.stt] && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-8 h-8 rounded-full border-2 border-white shadow-lg\",\n                  style: {\n                    backgroundColor: selectedColors[player.stt]\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 130,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white text-sm\",\n                  children: getColorName(selectedColors[player.stt])\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 134,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleRemoveColor(player.stt),\n                  className: \"text-red-400 hover:text-red-300 ml-2\",\n                  children: /*#__PURE__*/_jsxDEV(XMarkIcon, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 141,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 137,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-4 sm:grid-cols-6 lg:grid-cols-8 gap-2\",\n              children: AVAILABLE_COLORS.map(color => {\n                const isUsed = usedColors.has(color.value);\n                const isSelected = selectedColors[player.stt] === color.value;\n                const isDisabled = isUsed && !isSelected;\n                return /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => !isDisabled && handleColorSelect(player.stt, color.value),\n                  disabled: isDisabled,\n                  className: `\n                          relative w-10 h-10 sm:w-12 sm:h-12 rounded-lg border-2 transition-all duration-200 flex-shrink-0\n                          ${isSelected ? 'border-white scale-110 shadow-lg' : isDisabled ? 'border-gray-600 opacity-50 cursor-not-allowed' : 'border-gray-400 hover:border-white hover:scale-105'}\n                        `,\n                  style: {\n                    backgroundColor: color.value\n                  },\n                  title: isDisabled ? `${color.name} đã được sử dụng` : color.name,\n                  children: [isSelected && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute inset-0 flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-3 h-3 bg-white rounded-full\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 173,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 172,\n                    columnNumber: 27\n                  }, this), isDisabled && !isSelected && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute inset-0 flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(XMarkIcon, {\n                      className: \"w-6 h-6 text-gray-800\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 178,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 177,\n                    columnNumber: 27\n                  }, this)]\n                }, color.value, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 155,\n                  columnNumber: 23\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 17\n            }, this)]\n          }, player.stt || index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6 p-4 bg-blue-900/30 rounded-lg border border-blue-400/30\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-blue-200 font-semibold mb-2\",\n            children: \"H\\u01B0\\u1EDBng d\\u1EABn:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"text-blue-100 text-sm space-y-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u2022 Ch\\u1ECDn m\\xE0u kh\\xE1c nhau cho m\\u1ED7i th\\xED sinh\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u2022 M\\xE0u s\\u1EBD \\u0111\\u01B0\\u1EE3c s\\u1EED d\\u1EE5ng \\u0111\\u1EC3 t\\xF4 c\\xE1c \\xF4 tr\\xEAn b\\u1EA3ng 5x5 trong v\\xF2ng 4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u2022 M\\xE0u \\u0111\\xE3 ch\\u1ECDn s\\u1EBD hi\\u1EC3n th\\u1ECB trong \\xF4 th\\xED sinh\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u2022 B\\u1EA5m X \\u0111\\u1EC3 b\\u1ECF ch\\u1ECDn m\\xE0u cho th\\xED sinh\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-slate-800/80 px-4 py-3 border-t border-slate-600/50\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col sm:flex-row justify-between items-center gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-gray-400 text-sm\",\n            children: [\"\\u0110\\xE3 ch\\u1ECDn: \", Object.keys(selectedColors).length, \"/\", players.length, \" th\\xED sinh\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: onClose,\n              className: \"px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg font-medium transition-colors text-sm\",\n              children: \"H\\u1EE7y\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleSave,\n              className: \"px-6 py-2 bg-gradient-to-r from-purple-600 to-purple-500 hover:from-purple-700 hover:to-purple-600 text-white rounded-lg font-medium transition-all duration-200 hover:scale-105 text-sm\",\n              children: \"\\u2713 X\\xE1c nh\\u1EADn m\\xE0u\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 81,\n    columnNumber: 5\n  }, this);\n};\n_s(PlayerColorSelector, \"sdX5NupmesqCqyFJOYBzLKuIIC4=\");\n_c = PlayerColorSelector;\nexport default PlayerColorSelector;\nvar _c;\n$RefreshReg$(_c, \"PlayerColorSelector\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "XMarkIcon", "PaintBrushIcon", "jsxDEV", "_jsxDEV", "AVAILABLE_COLORS", "name", "value", "bg", "PlayerColorSelector", "isOpen", "onClose", "players", "onSaveColors", "currentColors", "_s", "selectedColors", "setSelectedColors", "usedColors", "setUsedColors", "Set", "Object", "values", "handleColorSelect", "player<PERSON>tt", "color", "newSelectedColors", "newUsedColors", "delete", "add", "handleRemoveColor", "handleSave", "getColorName", "colorValue", "find", "c", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "player", "index", "src", "avatar", "alt", "userName", "stt", "style", "backgroundColor", "isUsed", "has", "isSelected", "isDisabled", "disabled", "title", "keys", "length", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/components/PlayerColorSelector.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { XMarkIcon, PaintBrushIcon } from '@heroicons/react/24/outline';\n\ninterface PlayerColorSelectorProps {\n  isOpen: boolean;\n  onClose: () => void;\n  players: any[];\n  onSaveColors: (colors: Record<string, string>) => void;\n  currentColors?: Record<string, string>;\n}\n\nconst AVAILABLE_COLORS = [\n  { name: 'Đỏ', value: '#FF0000', bg: 'bg-red-500' },\n  { name: 'Xanh lá', value: '#00FF00', bg: 'bg-green-500' },\n  { name: '<PERSON>anh dương', value: '#0000FF', bg: 'bg-blue-500' },\n  { name: 'Vàng', value: '#FFFF00', bg: 'bg-yellow-500' },\n  { name: 'Tím', value: '#800080', bg: 'bg-purple-500' },\n  { name: 'Cam', value: '#FFA500', bg: 'bg-orange-500' },\n  { name: '<PERSON>ồng', value: '#FF69B4', bg: 'bg-pink-500' },\n  { name: '<PERSON>anh lam', value: '#00FFFF', bg: 'bg-cyan-500' }\n];\n\nconst PlayerColorSelector: React.FC<PlayerColorSelectorProps> = ({ \n  isOpen, \n  onClose, \n  players, \n  onSaveColors,\n  currentColors = {}\n}) => {\n  const [selectedColors, setSelectedColors] = useState<Record<string, string>>(currentColors);\n  const [usedColors, setUsedColors] = useState<Set<string>>(new Set());\n\n  useEffect(() => {\n    setSelectedColors(currentColors);\n    setUsedColors(new Set(Object.values(currentColors)));\n  }, [currentColors, isOpen]);\n\n  if (!isOpen) return null;\n\n  const handleColorSelect = (playerStt: string, color: string) => {\n    const newSelectedColors = { ...selectedColors };\n    const newUsedColors = new Set(usedColors);\n\n    // Remove old color if exists\n    if (selectedColors[playerStt]) {\n      newUsedColors.delete(selectedColors[playerStt]);\n    }\n\n    // Add new color\n    newSelectedColors[playerStt] = color;\n    newUsedColors.add(color);\n\n    setSelectedColors(newSelectedColors);\n    setUsedColors(newUsedColors);\n  };\n\n  const handleRemoveColor = (playerStt: string) => {\n    const newSelectedColors = { ...selectedColors };\n    const newUsedColors = new Set(usedColors);\n\n    if (selectedColors[playerStt]) {\n      newUsedColors.delete(selectedColors[playerStt]);\n      delete newSelectedColors[playerStt];\n    }\n\n    setSelectedColors(newSelectedColors);\n    setUsedColors(newUsedColors);\n  };\n\n  const handleSave = () => {\n    onSaveColors(selectedColors);\n    onClose();\n  };\n\n  const getColorName = (colorValue: string) => {\n    const color = AVAILABLE_COLORS.find(c => c.value === colorValue);\n    return color ? color.name : colorValue;\n  };\n\n  return (\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center\">\n      {/* Backdrop */}\n      <div\n        className=\"absolute inset-0 bg-black/70 backdrop-blur-sm\"\n        onClick={onClose}\n      />\n\n      {/* Modal */}\n      <div className=\"relative bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl shadow-2xl border border-purple-400/30 max-w-5xl w-full mx-4 max-h-[90vh] overflow-hidden\">\n        {/* Header */}\n        <div className=\"bg-gradient-to-r from-purple-600 to-pink-600 px-4 py-3 flex items-center justify-between\">\n          <div className=\"flex items-center space-x-3\">\n            <PaintBrushIcon className=\"w-6 h-6 text-white\" />\n            <h2 className=\"text-lg font-bold text-white\">\n              Chọn màu cho thí sinh - Vòng 4\n            </h2>\n          </div>\n          <button\n            onClick={onClose}\n            className=\"text-white hover:text-gray-300 transition-colors p-1 rounded-lg hover:bg-white/10\"\n          >\n            <XMarkIcon className=\"w-5 h-5\" />\n          </button>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-4 overflow-y-auto max-h-[75vh]\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-4\">\n            {players.map((player, index) => (\n              <div\n                key={player.stt || index}\n                className=\"bg-slate-700/50 rounded-lg p-4 border border-slate-600/50\"\n              >\n                <div className=\"flex items-center justify-between mb-4\">\n                  <div className=\"flex items-center space-x-3\">\n                    <img\n                      src={player.avatar || '/default-avatar.png'}\n                      alt={player.userName}\n                      className=\"w-10 h-10 rounded-full\"\n                    />\n                    <div>\n                      <h3 className=\"text-white font-semibold\">{player.userName}</h3>\n                      <p className=\"text-gray-400 text-sm\">Thí sinh {player.stt}</p>\n                    </div>\n                  </div>\n                  \n                  {/* Current Color Display */}\n                  {selectedColors[player.stt] && (\n                    <div className=\"flex items-center space-x-2\">\n                      <div\n                        className=\"w-8 h-8 rounded-full border-2 border-white shadow-lg\"\n                        style={{ backgroundColor: selectedColors[player.stt] }}\n                      ></div>\n                      <span className=\"text-white text-sm\">\n                        {getColorName(selectedColors[player.stt])}\n                      </span>\n                      <button\n                        onClick={() => handleRemoveColor(player.stt)}\n                        className=\"text-red-400 hover:text-red-300 ml-2\"\n                      >\n                        <XMarkIcon className=\"w-4 h-4\" />\n                      </button>\n                    </div>\n                  )}\n                </div>\n\n                {/* Color Options */}\n                <div className=\"grid grid-cols-4 sm:grid-cols-6 lg:grid-cols-8 gap-2\">\n                  {AVAILABLE_COLORS.map((color) => {\n                    const isUsed = usedColors.has(color.value);\n                    const isSelected = selectedColors[player.stt] === color.value;\n                    const isDisabled = isUsed && !isSelected;\n\n                    return (\n                      <button\n                        key={color.value}\n                        onClick={() => !isDisabled && handleColorSelect(player.stt, color.value)}\n                        disabled={isDisabled}\n                        className={`\n                          relative w-10 h-10 sm:w-12 sm:h-12 rounded-lg border-2 transition-all duration-200 flex-shrink-0\n                          ${isSelected\n                            ? 'border-white scale-110 shadow-lg'\n                            : isDisabled\n                              ? 'border-gray-600 opacity-50 cursor-not-allowed'\n                              : 'border-gray-400 hover:border-white hover:scale-105'\n                          }\n                        `}\n                        style={{ backgroundColor: color.value }}\n                        title={isDisabled ? `${color.name} đã được sử dụng` : color.name}\n                      >\n                        {isSelected && (\n                          <div className=\"absolute inset-0 flex items-center justify-center\">\n                            <div className=\"w-3 h-3 bg-white rounded-full\"></div>\n                          </div>\n                        )}\n                        {isDisabled && !isSelected && (\n                          <div className=\"absolute inset-0 flex items-center justify-center\">\n                            <XMarkIcon className=\"w-6 h-6 text-gray-800\" />\n                          </div>\n                        )}\n                      </button>\n                    );\n                  })}\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {/* Instructions */}\n          <div className=\"mt-6 p-4 bg-blue-900/30 rounded-lg border border-blue-400/30\">\n            <h3 className=\"text-blue-200 font-semibold mb-2\">Hướng dẫn:</h3>\n            <ul className=\"text-blue-100 text-sm space-y-1\">\n              <li>• Chọn màu khác nhau cho mỗi thí sinh</li>\n              <li>• Màu sẽ được sử dụng để tô các ô trên bảng 5x5 trong vòng 4</li>\n              <li>• Màu đã chọn sẽ hiển thị trong ô thí sinh</li>\n              <li>• Bấm X để bỏ chọn màu cho thí sinh</li>\n            </ul>\n          </div>\n        </div>\n\n        {/* Footer */}\n        <div className=\"bg-slate-800/80 px-4 py-3 border-t border-slate-600/50\">\n          <div className=\"flex flex-col sm:flex-row justify-between items-center gap-3\">\n            <div className=\"text-gray-400 text-sm\">\n              Đã chọn: {Object.keys(selectedColors).length}/{players.length} thí sinh\n            </div>\n            <div className=\"flex space-x-3\">\n              <button\n                onClick={onClose}\n                className=\"px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg font-medium transition-colors text-sm\"\n              >\n                Hủy\n              </button>\n              <button\n                onClick={handleSave}\n                className=\"px-6 py-2 bg-gradient-to-r from-purple-600 to-purple-500 hover:from-purple-700 hover:to-purple-600 text-white rounded-lg font-medium transition-all duration-200 hover:scale-105 text-sm\"\n              >\n                ✓ Xác nhận màu\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PlayerColorSelector;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,cAAc,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAUxE,MAAMC,gBAAgB,GAAG,CACvB;EAAEC,IAAI,EAAE,IAAI;EAAEC,KAAK,EAAE,SAAS;EAAEC,EAAE,EAAE;AAAa,CAAC,EAClD;EAAEF,IAAI,EAAE,SAAS;EAAEC,KAAK,EAAE,SAAS;EAAEC,EAAE,EAAE;AAAe,CAAC,EACzD;EAAEF,IAAI,EAAE,YAAY;EAAEC,KAAK,EAAE,SAAS;EAAEC,EAAE,EAAE;AAAc,CAAC,EAC3D;EAAEF,IAAI,EAAE,MAAM;EAAEC,KAAK,EAAE,SAAS;EAAEC,EAAE,EAAE;AAAgB,CAAC,EACvD;EAAEF,IAAI,EAAE,KAAK;EAAEC,KAAK,EAAE,SAAS;EAAEC,EAAE,EAAE;AAAgB,CAAC,EACtD;EAAEF,IAAI,EAAE,KAAK;EAAEC,KAAK,EAAE,SAAS;EAAEC,EAAE,EAAE;AAAgB,CAAC,EACtD;EAAEF,IAAI,EAAE,MAAM;EAAEC,KAAK,EAAE,SAAS;EAAEC,EAAE,EAAE;AAAc,CAAC,EACrD;EAAEF,IAAI,EAAE,UAAU;EAAEC,KAAK,EAAE,SAAS;EAAEC,EAAE,EAAE;AAAc,CAAC,CAC1D;AAED,MAAMC,mBAAuD,GAAGA,CAAC;EAC/DC,MAAM;EACNC,OAAO;EACPC,OAAO;EACPC,YAAY;EACZC,aAAa,GAAG,CAAC;AACnB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGlB,QAAQ,CAAyBe,aAAa,CAAC;EAC3F,MAAM,CAACI,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAc,IAAIqB,GAAG,CAAC,CAAC,CAAC;EAEpEpB,SAAS,CAAC,MAAM;IACdiB,iBAAiB,CAACH,aAAa,CAAC;IAChCK,aAAa,CAAC,IAAIC,GAAG,CAACC,MAAM,CAACC,MAAM,CAACR,aAAa,CAAC,CAAC,CAAC;EACtD,CAAC,EAAE,CAACA,aAAa,EAAEJ,MAAM,CAAC,CAAC;EAE3B,IAAI,CAACA,MAAM,EAAE,OAAO,IAAI;EAExB,MAAMa,iBAAiB,GAAGA,CAACC,SAAiB,EAAEC,KAAa,KAAK;IAC9D,MAAMC,iBAAiB,GAAG;MAAE,GAAGV;IAAe,CAAC;IAC/C,MAAMW,aAAa,GAAG,IAAIP,GAAG,CAACF,UAAU,CAAC;;IAEzC;IACA,IAAIF,cAAc,CAACQ,SAAS,CAAC,EAAE;MAC7BG,aAAa,CAACC,MAAM,CAACZ,cAAc,CAACQ,SAAS,CAAC,CAAC;IACjD;;IAEA;IACAE,iBAAiB,CAACF,SAAS,CAAC,GAAGC,KAAK;IACpCE,aAAa,CAACE,GAAG,CAACJ,KAAK,CAAC;IAExBR,iBAAiB,CAACS,iBAAiB,CAAC;IACpCP,aAAa,CAACQ,aAAa,CAAC;EAC9B,CAAC;EAED,MAAMG,iBAAiB,GAAIN,SAAiB,IAAK;IAC/C,MAAME,iBAAiB,GAAG;MAAE,GAAGV;IAAe,CAAC;IAC/C,MAAMW,aAAa,GAAG,IAAIP,GAAG,CAACF,UAAU,CAAC;IAEzC,IAAIF,cAAc,CAACQ,SAAS,CAAC,EAAE;MAC7BG,aAAa,CAACC,MAAM,CAACZ,cAAc,CAACQ,SAAS,CAAC,CAAC;MAC/C,OAAOE,iBAAiB,CAACF,SAAS,CAAC;IACrC;IAEAP,iBAAiB,CAACS,iBAAiB,CAAC;IACpCP,aAAa,CAACQ,aAAa,CAAC;EAC9B,CAAC;EAED,MAAMI,UAAU,GAAGA,CAAA,KAAM;IACvBlB,YAAY,CAACG,cAAc,CAAC;IAC5BL,OAAO,CAAC,CAAC;EACX,CAAC;EAED,MAAMqB,YAAY,GAAIC,UAAkB,IAAK;IAC3C,MAAMR,KAAK,GAAGpB,gBAAgB,CAAC6B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5B,KAAK,KAAK0B,UAAU,CAAC;IAChE,OAAOR,KAAK,GAAGA,KAAK,CAACnB,IAAI,GAAG2B,UAAU;EACxC,CAAC;EAED,oBACE7B,OAAA;IAAKgC,SAAS,EAAC,qDAAqD;IAAAC,QAAA,gBAElEjC,OAAA;MACEgC,SAAS,EAAC,+CAA+C;MACzDE,OAAO,EAAE3B;IAAQ;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC,eAGFtC,OAAA;MAAKgC,SAAS,EAAC,8JAA8J;MAAAC,QAAA,gBAE3KjC,OAAA;QAAKgC,SAAS,EAAC,0FAA0F;QAAAC,QAAA,gBACvGjC,OAAA;UAAKgC,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CjC,OAAA,CAACF,cAAc;YAACkC,SAAS,EAAC;UAAoB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjDtC,OAAA;YAAIgC,SAAS,EAAC,8BAA8B;YAAAC,QAAA,EAAC;UAE7C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACNtC,OAAA;UACEkC,OAAO,EAAE3B,OAAQ;UACjByB,SAAS,EAAC,mFAAmF;UAAAC,QAAA,eAE7FjC,OAAA,CAACH,SAAS;YAACmC,SAAS,EAAC;UAAS;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNtC,OAAA;QAAKgC,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/CjC,OAAA;UAAKgC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACnDzB,OAAO,CAAC+B,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBACzBzC,OAAA;YAEEgC,SAAS,EAAC,2DAA2D;YAAAC,QAAA,gBAErEjC,OAAA;cAAKgC,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDjC,OAAA;gBAAKgC,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CjC,OAAA;kBACE0C,GAAG,EAAEF,MAAM,CAACG,MAAM,IAAI,qBAAsB;kBAC5CC,GAAG,EAAEJ,MAAM,CAACK,QAAS;kBACrBb,SAAS,EAAC;gBAAwB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACFtC,OAAA;kBAAAiC,QAAA,gBACEjC,OAAA;oBAAIgC,SAAS,EAAC,0BAA0B;oBAAAC,QAAA,EAAEO,MAAM,CAACK;kBAAQ;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC/DtC,OAAA;oBAAGgC,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GAAC,cAAS,EAACO,MAAM,CAACM,GAAG;kBAAA;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGL1B,cAAc,CAAC4B,MAAM,CAACM,GAAG,CAAC,iBACzB9C,OAAA;gBAAKgC,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CjC,OAAA;kBACEgC,SAAS,EAAC,sDAAsD;kBAChEe,KAAK,EAAE;oBAAEC,eAAe,EAAEpC,cAAc,CAAC4B,MAAM,CAACM,GAAG;kBAAE;gBAAE;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC,eACPtC,OAAA;kBAAMgC,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EACjCL,YAAY,CAAChB,cAAc,CAAC4B,MAAM,CAACM,GAAG,CAAC;gBAAC;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CAAC,eACPtC,OAAA;kBACEkC,OAAO,EAAEA,CAAA,KAAMR,iBAAiB,CAACc,MAAM,CAACM,GAAG,CAAE;kBAC7Cd,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,eAEhDjC,OAAA,CAACH,SAAS;oBAACmC,SAAS,EAAC;kBAAS;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNtC,OAAA;cAAKgC,SAAS,EAAC,sDAAsD;cAAAC,QAAA,EAClEhC,gBAAgB,CAACsC,GAAG,CAAElB,KAAK,IAAK;gBAC/B,MAAM4B,MAAM,GAAGnC,UAAU,CAACoC,GAAG,CAAC7B,KAAK,CAAClB,KAAK,CAAC;gBAC1C,MAAMgD,UAAU,GAAGvC,cAAc,CAAC4B,MAAM,CAACM,GAAG,CAAC,KAAKzB,KAAK,CAAClB,KAAK;gBAC7D,MAAMiD,UAAU,GAAGH,MAAM,IAAI,CAACE,UAAU;gBAExC,oBACEnD,OAAA;kBAEEkC,OAAO,EAAEA,CAAA,KAAM,CAACkB,UAAU,IAAIjC,iBAAiB,CAACqB,MAAM,CAACM,GAAG,EAAEzB,KAAK,CAAClB,KAAK,CAAE;kBACzEkD,QAAQ,EAAED,UAAW;kBACrBpB,SAAS,EAAE;AACnC;AACA,4BAA4BmB,UAAU,GACR,kCAAkC,GAClCC,UAAU,GACR,+CAA+C,GAC/C,oDAAoD;AACpF,yBAC0B;kBACFL,KAAK,EAAE;oBAAEC,eAAe,EAAE3B,KAAK,CAAClB;kBAAM,CAAE;kBACxCmD,KAAK,EAAEF,UAAU,GAAG,GAAG/B,KAAK,CAACnB,IAAI,kBAAkB,GAAGmB,KAAK,CAACnB,IAAK;kBAAA+B,QAAA,GAEhEkB,UAAU,iBACTnD,OAAA;oBAAKgC,SAAS,EAAC,mDAAmD;oBAAAC,QAAA,eAChEjC,OAAA;sBAAKgC,SAAS,EAAC;oBAA+B;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CACN,EACAc,UAAU,IAAI,CAACD,UAAU,iBACxBnD,OAAA;oBAAKgC,SAAS,EAAC,mDAAmD;oBAAAC,QAAA,eAChEjC,OAAA,CAACH,SAAS;sBAACmC,SAAS,EAAC;oBAAuB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CACN;gBAAA,GAxBIjB,KAAK,CAAClB,KAAK;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAyBV,CAAC;cAEb,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA,GAzEDE,MAAM,CAACM,GAAG,IAAIL,KAAK;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA0ErB,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNtC,OAAA;UAAKgC,SAAS,EAAC,8DAA8D;UAAAC,QAAA,gBAC3EjC,OAAA;YAAIgC,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChEtC,OAAA;YAAIgC,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC7CjC,OAAA;cAAAiC,QAAA,EAAI;YAAqC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9CtC,OAAA;cAAAiC,QAAA,EAAI;YAA4D;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrEtC,OAAA;cAAAiC,QAAA,EAAI;YAA0C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnDtC,OAAA;cAAAiC,QAAA,EAAI;YAAmC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtC,OAAA;QAAKgC,SAAS,EAAC,wDAAwD;QAAAC,QAAA,eACrEjC,OAAA;UAAKgC,SAAS,EAAC,8DAA8D;UAAAC,QAAA,gBAC3EjC,OAAA;YAAKgC,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GAAC,wBAC5B,EAAChB,MAAM,CAACsC,IAAI,CAAC3C,cAAc,CAAC,CAAC4C,MAAM,EAAC,GAAC,EAAChD,OAAO,CAACgD,MAAM,EAAC,cAChE;UAAA;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNtC,OAAA;YAAKgC,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BjC,OAAA;cACEkC,OAAO,EAAE3B,OAAQ;cACjByB,SAAS,EAAC,qGAAqG;cAAAC,QAAA,EAChH;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTtC,OAAA;cACEkC,OAAO,EAAEP,UAAW;cACpBK,SAAS,EAAC,0LAA0L;cAAAC,QAAA,EACrM;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3B,EAAA,CA3MIN,mBAAuD;AAAAoD,EAAA,GAAvDpD,mBAAuD;AA6M7D,eAAeA,mBAAmB;AAAC,IAAAoD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}